{"version": 3, "sources": ["../../../src/build/templates/middleware.ts"], "sourcesContent": ["import type { AdapterOptions } from '../../server/web/adapter'\n\nimport '../../server/web/globals'\n\nimport { adapter } from '../../server/web/adapter'\n\n// Import the userland code.\nimport * as _mod from 'VAR_USERLAND'\nimport { edgeInstrumentationOnRequestError } from '../../server/web/globals'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\n\nconst mod = { ..._mod }\nconst handler = mod.middleware || mod.default\n\nconst page = 'VAR_DEFINITION_PAGE'\n\nif (typeof handler !== 'function') {\n  throw new Error(\n    `The Middleware \"${page}\" must export a \\`middleware\\` or a \\`default\\` function`\n  )\n}\n\n// Middleware will only sent out the FetchEvent to next server,\n// so load instrumentation module here and track the error inside middleware module.\nfunction errorHandledHandler(fn: AdapterOptions['handler']) {\n  return async (...args: Parameters<AdapterOptions['handler']>) => {\n    try {\n      return await fn(...args)\n    } catch (err) {\n      // In development, error the navigation API usage in runtime,\n      // since it's not allowed to be used in middleware as it's outside of react component tree.\n      if (process.env.NODE_ENV !== 'production') {\n        if (isNextRouterError(err)) {\n          err.message = `Next.js navigation API is not allowed to be used in Middleware.`\n          throw err\n        }\n      }\n      const req = args[0]\n      const url = new URL(req.url)\n      const resource = url.pathname + url.search\n      await edgeInstrumentationOnRequestError(\n        err,\n        {\n          path: resource,\n          method: req.method,\n          headers: Object.fromEntries(req.headers.entries()),\n        },\n        {\n          routerKind: 'Pages Router',\n          routePath: '/middleware',\n          routeType: 'middleware',\n          revalidateReason: undefined,\n        }\n      )\n\n      throw err\n    }\n  }\n}\n\nexport default function nHandler(\n  opts: Omit<AdapterOptions, 'IncrementalCache' | 'page' | 'handler'>\n) {\n  return adapter({\n    ...opts,\n    page,\n    handler: errorHandledHandler(handler),\n  })\n}\n"], "names": ["adapter", "_mod", "edgeInstrumentationOnRequestError", "isNextRouterError", "mod", "handler", "middleware", "default", "page", "Error", "error<PERSON>and<PERSON><PERSON>andler", "fn", "args", "err", "process", "env", "NODE_ENV", "message", "req", "url", "URL", "resource", "pathname", "search", "path", "method", "headers", "Object", "fromEntries", "entries", "routerKind", "routePath", "routeType", "revalidateReason", "undefined", "nH<PERSON><PERSON>", "opts"], "mappings": "AAEA,OAAO,2BAA0B;AAEjC,SAASA,OAAO,QAAQ,2BAA0B;AAElD,4BAA4B;AAC5B,YAAYC,UAAU,eAAc;AACpC,SAASC,iCAAiC,QAAQ,2BAA0B;AAC5E,SAASC,iBAAiB,QAAQ,+CAA8C;AAEhF,MAAMC,MAAM;IAAE,GAAGH,IAAI;AAAC;AACtB,MAAMI,UAAUD,IAAIE,UAAU,IAAIF,IAAIG,OAAO;AAE7C,MAAMC,OAAO;AAEb,IAAI,OAAOH,YAAY,YAAY;IACjC,MAAM,qBAEL,CAFK,IAAII,MACR,CAAC,gBAAgB,EAAED,KAAK,wDAAwD,CAAC,GAD7E,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,+DAA+D;AAC/D,oFAAoF;AACpF,SAASE,oBAAoBC,EAA6B;IACxD,OAAO,OAAO,GAAGC;QACf,IAAI;YACF,OAAO,MAAMD,MAAMC;QACrB,EAAE,OAAOC,KAAK;YACZ,6DAA6D;YAC7D,2FAA2F;YAC3F,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAIb,kBAAkBU,MAAM;oBAC1BA,IAAII,OAAO,GAAG,CAAC,+DAA+D,CAAC;oBAC/E,MAAMJ;gBACR;YACF;YACA,MAAMK,MAAMN,IAAI,CAAC,EAAE;YACnB,MAAMO,MAAM,IAAIC,IAAIF,IAAIC,GAAG;YAC3B,MAAME,WAAWF,IAAIG,QAAQ,GAAGH,IAAII,MAAM;YAC1C,MAAMrB,kCACJW,KACA;gBACEW,MAAMH;gBACNI,QAAQP,IAAIO,MAAM;gBAClBC,SAASC,OAAOC,WAAW,CAACV,IAAIQ,OAAO,CAACG,OAAO;YACjD,GACA;gBACEC,YAAY;gBACZC,WAAW;gBACXC,WAAW;gBACXC,kBAAkBC;YACpB;YAGF,MAAMrB;QACR;IACF;AACF;AAEA,eAAe,SAASsB,SACtBC,IAAmE;IAEnE,OAAOpC,QAAQ;QACb,GAAGoC,IAAI;QACP5B;QACAH,SAASK,oBAAoBL;IAC/B;AACF"}