{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-flight-client-module-loader.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { getRSCModuleInformation } from '../../analysis/get-page-static-info'\nimport { getModuleBuildInfo } from './get-module-build-info'\n\nconst flightClientModuleLoader: webpack.LoaderDefinitionFunction =\n  function transformSource(this, source: string, sourceMap: any) {\n    // Avoid buffer to be consumed\n    if (typeof source !== 'string') {\n      throw new Error('Expected source to have been transformed to a string.')\n    }\n\n    if (!this._module) {\n      return source\n    }\n    // Assign the RSC meta information to buildInfo.\n    const buildInfo = getModuleBuildInfo(this._module)\n    buildInfo.rsc = getRSCModuleInformation(source, false)\n    let prefix = ''\n    if (process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN) {\n      const rscModuleInformationJson = JSON.stringify(buildInfo.rsc)\n      prefix = `/* __rspack_internal_rsc_module_information_do_not_use__ ${rscModuleInformationJson} */\\n`\n      source = prefix + source\n    }\n\n    // This is a server action entry module in the client layer. We need to\n    // create re-exports of \"virtual modules\" to expose the reference IDs to the\n    // client separately so they won't be always in the same one module which is\n    // not splittable. This server action module tree shaking is only applied in\n    // production mode. In development mode, we want to preserve the original\n    // modules (as transformed by SWC) to ensure that source mapping works.\n    if (buildInfo.rsc.actionIds && process.env.NODE_ENV === 'production') {\n      return (\n        prefix +\n        Object.entries(buildInfo.rsc.actionIds)\n          .map(([id, name]) => {\n            return `export { ${name} } from 'next-flight-server-reference-proxy-loader?id=${id}&name=${name}!'`\n          })\n          .join('\\n')\n      )\n    }\n\n    return this.callback(null, source, sourceMap)\n  }\n\nexport default flightClientModuleLoader\n"], "names": ["getRSCModuleInformation", "getModuleBuildInfo", "flightClientModuleLoader", "transformSource", "source", "sourceMap", "Error", "_module", "buildInfo", "rsc", "prefix", "process", "env", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "rscModuleInformationJson", "JSON", "stringify", "actionIds", "NODE_ENV", "Object", "entries", "map", "id", "name", "join", "callback"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,sCAAqC;AAC7E,SAASC,kBAAkB,QAAQ,0BAAyB;AAE5D,MAAMC,2BACJ,SAASC,gBAAsBC,MAAc,EAAEC,SAAc;IAC3D,8BAA8B;IAC9B,IAAI,OAAOD,WAAW,UAAU;QAC9B,MAAM,qBAAkE,CAAlE,IAAIE,MAAM,0DAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiE;IACzE;IAEA,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;QACjB,OAAOH;IACT;IACA,gDAAgD;IAChD,MAAMI,YAAYP,mBAAmB,IAAI,CAACM,OAAO;IACjDC,UAAUC,GAAG,GAAGT,wBAAwBI,QAAQ;IAChD,IAAIM,SAAS;IACb,IAAIC,QAAQC,GAAG,CAACC,kCAAkC,EAAE;QAClD,MAAMC,2BAA2BC,KAAKC,SAAS,CAACR,UAAUC,GAAG;QAC7DC,SAAS,CAAC,yDAAyD,EAAEI,yBAAyB,KAAK,CAAC;QACpGV,SAASM,SAASN;IACpB;IAEA,uEAAuE;IACvE,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IAC5E,yEAAyE;IACzE,uEAAuE;IACvE,IAAII,UAAUC,GAAG,CAACQ,SAAS,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,cAAc;QACpE,OACER,SACAS,OAAOC,OAAO,CAACZ,UAAUC,GAAG,CAACQ,SAAS,EACnCI,GAAG,CAAC,CAAC,CAACC,IAAIC,KAAK;YACd,OAAO,CAAC,SAAS,EAAEA,KAAK,sDAAsD,EAAED,GAAG,MAAM,EAAEC,KAAK,EAAE,CAAC;QACrG,GACCC,IAAI,CAAC;IAEZ;IAEA,OAAO,IAAI,CAACC,QAAQ,CAAC,MAAMrB,QAAQC;AACrC;AAEF,eAAeH,yBAAwB"}