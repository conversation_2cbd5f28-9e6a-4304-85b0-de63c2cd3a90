{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "sourcesContent": ["/*\nCopyright (c) 2017 The swc Project Developers\n\nPermission is hereby granted, free of charge, to any\nperson obtaining a copy of this software and associated\ndocumentation files (the \"Software\"), to deal in the\nSoftware without restriction, including without\nlimitation the rights to use, copy, modify, merge,\npublish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software\nis furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice\nshall be included in all copies or substantial portions\nof the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF\nANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED\nTO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A\nPARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT\nSHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR\nIN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\nDEALINGS IN THE SOFTWARE.\n*/\n\nimport type { NextConfig } from '../../../types'\nimport type { WebpackLayerName } from '../../../lib/constants'\nimport { isWasm, transform } from '../../swc'\nimport { getLoaderSWCOptions } from '../../swc/options'\nimport path, { isAbsolute } from 'path'\nimport { babelIncludeRegexes } from '../../webpack-config'\nimport { isResourceInPackages } from '../../handle-externals'\nimport type { TelemetryLoaderContext } from '../plugins/telemetry-plugin/telemetry-plugin'\nimport {\n  updateTelemetryLoaderCtxFromTransformOutput,\n  type SwcTransformTelemetryOutput,\n} from '../plugins/telemetry-plugin/update-telemetry-loader-context-from-swc'\nimport type { LoaderContext } from 'webpack'\n\nconst maybeExclude = (\n  excludePath: string,\n  transpilePackages: string[]\n): boolean => {\n  if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n    return false\n  }\n\n  const shouldBeBundled = isResourceInPackages(excludePath, transpilePackages)\n  if (shouldBeBundled) return false\n\n  return excludePath.includes('node_modules')\n}\n\nexport interface SWCLoaderOptions {\n  rootDir: string\n  isServer: boolean\n  pagesDir?: string\n  appDir?: string\n  hasReactRefresh: boolean\n  optimizeServerReact?: boolean\n  nextConfig: NextConfig\n  jsConfig: any\n  supportedBrowsers: string[] | undefined\n  swcCacheDir: string\n  serverComponents?: boolean\n  serverReferenceHashSalt: string\n  bundleLayer?: WebpackLayerName\n  esm?: boolean\n  transpilePackages?: string[]\n}\n\n// these are exact code conditions checked\n// for to force transpiling a `node_module`\nconst FORCE_TRANSPILE_CONDITIONS =\n  /(next\\/font|next\\/dynamic|use server|use client)/\n\nasync function loaderTransform(\n  this: LoaderContext<SWCLoaderOptions> & TelemetryLoaderContext,\n  source?: string,\n  inputSourceMap?: any\n) {\n  // Make the loader async\n  const filename = this.resourcePath\n\n  // Ensure `.d.ts` are not processed.\n  if (filename.endsWith('.d.ts')) {\n    return [source, inputSourceMap]\n  }\n\n  let loaderOptions: SWCLoaderOptions = this.getOptions() || {}\n  const shouldMaybeExclude = maybeExclude(\n    filename,\n    loaderOptions.transpilePackages || []\n  )\n\n  if (shouldMaybeExclude) {\n    if (!source) {\n      throw new Error(`Invariant might be excluded but missing source`)\n    }\n\n    if (!FORCE_TRANSPILE_CONDITIONS.test(source)) {\n      return [source, inputSourceMap]\n    }\n  }\n\n  const {\n    isServer,\n    rootDir,\n    pagesDir,\n    appDir,\n    hasReactRefresh,\n    nextConfig,\n    jsConfig,\n    supportedBrowsers,\n    swcCacheDir,\n    serverComponents,\n    serverReferenceHashSalt,\n    bundleLayer,\n    esm,\n  } = loaderOptions\n  const isPageFile = pagesDir ? filename.startsWith(pagesDir) : false\n  const relativeFilePathFromRoot = path.relative(rootDir, filename)\n\n  const swcOptions = getLoaderSWCOptions({\n    pagesDir,\n    appDir,\n    filename,\n    isServer,\n    isPageFile,\n    development:\n      this.mode === 'development' ||\n      !!nextConfig.experimental?.allowDevelopmentBuild,\n    isDynamicIo: nextConfig.experimental?.dynamicIO,\n    hasReactRefresh,\n    modularizeImports: nextConfig?.modularizeImports,\n    optimizePackageImports: nextConfig?.experimental?.optimizePackageImports,\n    swcPlugins: nextConfig?.experimental?.swcPlugins,\n    compilerOptions: nextConfig?.compiler,\n    optimizeServerReact: nextConfig?.experimental?.optimizeServerReact,\n    jsConfig,\n    supportedBrowsers,\n    swcCacheDir,\n    relativeFilePathFromRoot,\n    serverComponents,\n    serverReferenceHashSalt,\n    bundleLayer,\n    esm,\n    cacheHandlers: nextConfig.experimental?.cacheHandlers,\n    useCacheEnabled: nextConfig.experimental?.useCache,\n  })\n\n  const programmaticOptions = {\n    ...swcOptions,\n    filename,\n    inputSourceMap: inputSourceMap ? JSON.stringify(inputSourceMap) : undefined,\n\n    // Set the default sourcemap behavior based on Webpack's mapping flag,\n    sourceMaps: this.sourceMap,\n    inlineSourcesContent: this.sourceMap,\n\n    // Ensure that Webpack will get a full absolute path in the sourcemap\n    // so that it can properly map the module back to its internal cached\n    // modules.\n    sourceFileName: filename,\n  }\n\n  if (!programmaticOptions.inputSourceMap) {\n    delete programmaticOptions.inputSourceMap\n  }\n\n  // auto detect development mode\n  if (\n    this.mode &&\n    programmaticOptions.jsc &&\n    programmaticOptions.jsc.transform &&\n    programmaticOptions.jsc.transform.react &&\n    !Object.prototype.hasOwnProperty.call(\n      programmaticOptions.jsc.transform.react,\n      'development'\n    )\n  ) {\n    programmaticOptions.jsc.transform.react.development =\n      this.mode === 'development'\n  }\n\n  return transform(source as any, programmaticOptions).then(\n    (\n      output: {\n        code: string\n        map?: string\n      } & SwcTransformTelemetryOutput\n    ) => {\n      updateTelemetryLoaderCtxFromTransformOutput(this, output)\n      return [output.code, output.map ? JSON.parse(output.map) : undefined]\n    }\n  )\n}\n\nconst EXCLUDED_PATHS =\n  /[\\\\/](cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules|__virtual__)[\\\\/]/g\n\nexport function pitch(this: any) {\n  const callback = this.async()\n  let loaderOptions: SWCLoaderOptions = this.getOptions() || {}\n\n  const shouldMaybeExclude = maybeExclude(\n    this.resourcePath,\n    loaderOptions.transpilePackages || []\n  )\n\n  ;(async () => {\n    if (\n      // if it might be excluded/no-op we can't use pitch loader\n      !shouldMaybeExclude &&\n      // TODO: investigate swc file reading in PnP mode?\n      !process.versions.pnp &&\n      !EXCLUDED_PATHS.test(this.resourcePath) &&\n      this.loaders.length - 1 === this.loaderIndex &&\n      isAbsolute(this.resourcePath) &&\n      !(await isWasm())\n    ) {\n      this.addDependency(this.resourcePath)\n      return loaderTransform.call(this)\n    }\n  })().then((r) => {\n    if (r) return callback(null, ...r)\n    callback()\n  }, callback)\n}\n\nexport default function swcLoader(\n  this: any,\n  inputSource: string,\n  inputSourceMap: any\n) {\n  const callback = this.async()\n  loaderTransform.call(this, inputSource, inputSourceMap).then(\n    ([transformedSource, outputSourceMap]: any) => {\n      callback(null, transformedSource, outputSourceMap || inputSourceMap)\n    },\n    (err: Error) => {\n      callback(err)\n    }\n  )\n}\n\n// accept Buffers instead of strings\nexport const raw = true\n"], "names": ["isWasm", "transform", "getLoaderSWCOptions", "path", "isAbsolute", "babelIncludeRegexes", "isResourceInPackages", "updateTelemetryLoaderCtxFromTransformOutput", "maybeExclude", "excludePath", "transpilePackages", "some", "r", "test", "shouldBeBundled", "includes", "FORCE_TRANSPILE_CONDITIONS", "loaderTransform", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "endsWith", "loaderOptions", "getOptions", "shouldMaybeExclude", "Error", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "serverComponents", "serverReferenceHashSalt", "bundleLayer", "esm", "isPageFile", "startsWith", "relativeFilePathFromRoot", "relative", "swcOptions", "development", "mode", "experimental", "allowDevelopmentBuild", "isDynamicIo", "dynamicIO", "modularizeImports", "optimizePackageImports", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "cacheHandlers", "useCacheEnabled", "useCache", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "react", "Object", "prototype", "hasOwnProperty", "call", "then", "output", "code", "map", "parse", "EXCLUDED_PATHS", "pitch", "callback", "async", "process", "versions", "pnp", "loaders", "length", "loaderIndex", "addDependency", "sw<PERSON><PERSON><PERSON><PERSON>", "inputSource", "transformedSource", "outputSourceMap", "err", "raw"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,GAIA,SAASA,MAAM,EAAEC,SAAS,QAAQ,YAAW;AAC7C,SAASC,mBAAmB,QAAQ,oBAAmB;AACvD,OAAOC,QAAQC,UAAU,QAAQ,OAAM;AACvC,SAASC,mBAAmB,QAAQ,uBAAsB;AAC1D,SAASC,oBAAoB,QAAQ,yBAAwB;AAE7D,SACEC,2CAA2C,QAEtC,uEAAsE;AAG7E,MAAMC,eAAe,CACnBC,aACAC;IAEA,IAAIL,oBAAoBM,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,CAACJ,eAAe;QACxD,OAAO;IACT;IAEA,MAAMK,kBAAkBR,qBAAqBG,aAAaC;IAC1D,IAAII,iBAAiB,OAAO;IAE5B,OAAOL,YAAYM,QAAQ,CAAC;AAC9B;AAoBA,0CAA0C;AAC1C,2CAA2C;AAC3C,MAAMC,6BACJ;AAEF,eAAeC,gBAEbC,MAAe,EACfC,cAAoB;QAoDdC,0BACSA,2BAGWA,2BACZA,2BAESA,2BASNA,2BACEA;IAnEnB,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,oCAAoC;IACpC,IAAID,SAASE,QAAQ,CAAC,UAAU;QAC9B,OAAO;YAACL;YAAQC;SAAe;IACjC;IAEA,IAAIK,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAC5D,MAAMC,qBAAqBlB,aACzBa,UACAG,cAAcd,iBAAiB,IAAI,EAAE;IAGvC,IAAIgB,oBAAoB;QACtB,IAAI,CAACR,QAAQ;YACX,MAAM,qBAA2D,CAA3D,IAAIS,MAAM,CAAC,8CAA8C,CAAC,GAA1D,qBAAA;uBAAA;4BAAA;8BAAA;YAA0D;QAClE;QAEA,IAAI,CAACX,2BAA2BH,IAAI,CAACK,SAAS;YAC5C,OAAO;gBAACA;gBAAQC;aAAe;QACjC;IACF;IAEA,MAAM,EACJS,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfZ,UAAU,EACVa,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACXC,GAAG,EACJ,GAAGf;IACJ,MAAMgB,aAAaV,WAAWT,SAASoB,UAAU,CAACX,YAAY;IAC9D,MAAMY,2BAA2BvC,KAAKwC,QAAQ,CAACd,SAASR;IAExD,MAAMuB,aAAa1C,oBAAoB;QACrC4B;QACAC;QACAV;QACAO;QACAY;QACAK,aACE,IAAI,CAACC,IAAI,KAAK,iBACd,CAAC,GAAC1B,2BAAAA,WAAW2B,YAAY,qBAAvB3B,yBAAyB4B,qBAAqB;QAClDC,WAAW,GAAE7B,4BAAAA,WAAW2B,YAAY,qBAAvB3B,0BAAyB8B,SAAS;QAC/ClB;QACAmB,iBAAiB,EAAE/B,8BAAAA,WAAY+B,iBAAiB;QAChDC,sBAAsB,EAAEhC,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0BgC,sBAAsB;QACxEC,UAAU,EAAEjC,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0BiC,UAAU;QAChDC,eAAe,EAAElC,8BAAAA,WAAYmC,QAAQ;QACrCC,mBAAmB,EAAEpC,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0BoC,mBAAmB;QAClEvB;QACAC;QACAC;QACAO;QACAN;QACAC;QACAC;QACAC;QACAkB,aAAa,GAAErC,4BAAAA,WAAW2B,YAAY,qBAAvB3B,0BAAyBqC,aAAa;QACrDC,eAAe,GAAEtC,4BAAAA,WAAW2B,YAAY,qBAAvB3B,0BAAyBuC,QAAQ;IACpD;IAEA,MAAMC,sBAAsB;QAC1B,GAAGhB,UAAU;QACbvB;QACAF,gBAAgBA,iBAAiB0C,KAAKC,SAAS,CAAC3C,kBAAkB4C;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgB9C;IAClB;IAEA,IAAI,CAACuC,oBAAoBzC,cAAc,EAAE;QACvC,OAAOyC,oBAAoBzC,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAAC2B,IAAI,IACTc,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAACnE,SAAS,IACjC2D,oBAAoBQ,GAAG,CAACnE,SAAS,CAACoE,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCb,oBAAoBQ,GAAG,CAACnE,SAAS,CAACoE,KAAK,EACvC,gBAEF;QACAT,oBAAoBQ,GAAG,CAACnE,SAAS,CAACoE,KAAK,CAACxB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,OAAO7C,UAAUiB,QAAe0C,qBAAqBc,IAAI,CACvD,CACEC;QAKApE,4CAA4C,IAAI,EAAEoE;QAClD,OAAO;YAACA,OAAOC,IAAI;YAAED,OAAOE,GAAG,GAAGhB,KAAKiB,KAAK,CAACH,OAAOE,GAAG,IAAId;SAAU;IACvE;AAEJ;AAEA,MAAMgB,iBACJ;AAEF,OAAO,SAASC;IACd,MAAMC,WAAW,IAAI,CAACC,KAAK;IAC3B,IAAI1D,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAMC,qBAAqBlB,aACzB,IAAI,CAACc,YAAY,EACjBE,cAAcd,iBAAiB,IAAI,EAAE;IAGrC,CAAA;QACA,IACE,0DAA0D;QAC1D,CAACgB,sBACD,kDAAkD;QAClD,CAACyD,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACN,eAAelE,IAAI,CAAC,IAAI,CAACS,YAAY,KACtC,IAAI,CAACgE,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5CpF,WAAW,IAAI,CAACkB,YAAY,KAC5B,CAAE,MAAMtB,UACR;YACA,IAAI,CAACyF,aAAa,CAAC,IAAI,CAACnE,YAAY;YACpC,OAAOL,gBAAgBwD,IAAI,CAAC,IAAI;QAClC;IACF,CAAA,IAAKC,IAAI,CAAC,CAAC9D;QACT,IAAIA,GAAG,OAAOqE,SAAS,SAASrE;QAChCqE;IACF,GAAGA;AACL;AAEA,eAAe,SAASS,UAEtBC,WAAmB,EACnBxE,cAAmB;IAEnB,MAAM8D,WAAW,IAAI,CAACC,KAAK;IAC3BjE,gBAAgBwD,IAAI,CAAC,IAAI,EAAEkB,aAAaxE,gBAAgBuD,IAAI,CAC1D,CAAC,CAACkB,mBAAmBC,gBAAqB;QACxCZ,SAAS,MAAMW,mBAAmBC,mBAAmB1E;IACvD,GACA,CAAC2E;QACCb,SAASa;IACX;AAEJ;AAEA,oCAAoC;AACpC,OAAO,MAAMC,MAAM,KAAI"}