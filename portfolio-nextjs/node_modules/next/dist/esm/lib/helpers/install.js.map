{"version": 3, "sources": ["../../../src/lib/helpers/install.ts"], "sourcesContent": ["import { yellow } from '../picocolors'\nimport spawn from 'next/dist/compiled/cross-spawn'\nimport type { PackageManager } from './get-pkg-manager'\n\ninterface InstallArgs {\n  /**\n   * Indicate whether to install packages using npm, pnpm, or yarn.\n   */\n  packageManager: PackageManager\n  /**\n   * Indicate whether there is an active internet connection.\n   */\n  isOnline: boolean\n  /**\n   * Indicate whether the given dependencies are devDependencies.\n   */\n  devDependencies?: boolean\n}\n\n/**\n * Spawn a package manager installation with either npm, pnpm, or yarn.\n *\n * @returns A Promise that resolves once the installation is finished.\n */\nexport function install(\n  root: string,\n  dependencies: string[],\n  { packageManager, isOnline, devDependencies }: InstallArgs\n): Promise<void> {\n  let args: string[] = []\n\n  if (dependencies.length > 0) {\n    if (packageManager === 'yarn') {\n      args = ['add', '--exact']\n      if (devDependencies) args.push('--dev')\n    } else if (packageManager === 'pnpm') {\n      args = ['add', '--save-exact']\n      args.push(devDependencies ? '--save-dev' : '--save-prod')\n    } else {\n      // npm\n      args = ['install', '--save-exact']\n      args.push(devDependencies ? '--save-dev' : '--save')\n    }\n\n    args.push(...dependencies)\n  } else {\n    args = ['install'] // npm, pnpm, and yarn all support `install`\n\n    if (!isOnline) {\n      args.push('--offline')\n      console.log(yellow('You appear to be offline.'))\n      if (packageManager !== 'npm') {\n        console.log(\n          yellow(`Falling back to the local ${packageManager} cache.`)\n        )\n      }\n      console.log()\n    }\n  }\n\n  return new Promise((resolve, reject) => {\n    /**\n     * Spawn the installation process.\n     */\n    const child = spawn(packageManager, args, {\n      cwd: root,\n      stdio: 'inherit',\n      env: {\n        ...process.env,\n        ADBLOCK: '1',\n        // we set NODE_ENV to development as pnpm skips dev\n        // dependencies when production\n        NODE_ENV: 'development',\n        DISABLE_OPENCOLLECTIVE: '1',\n      },\n    })\n    child.on('close', (code) => {\n      if (code !== 0) {\n        reject({ command: `${packageManager} ${args.join(' ')}` })\n        return\n      }\n      resolve()\n    })\n  })\n}\n"], "names": ["yellow", "spawn", "install", "root", "dependencies", "packageManager", "isOnline", "devDependencies", "args", "length", "push", "console", "log", "Promise", "resolve", "reject", "child", "cwd", "stdio", "env", "process", "ADBLOCK", "NODE_ENV", "DISABLE_OPENCOLLECTIVE", "on", "code", "command", "join"], "mappings": "AAAA,SAASA,MAAM,QAAQ,gBAAe;AACtC,OAAOC,WAAW,iCAAgC;AAkBlD;;;;CAIC,GACD,OAAO,SAASC,QACdC,IAAY,EACZC,YAAsB,EACtB,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,eAAe,EAAe;IAE1D,IAAIC,OAAiB,EAAE;IAEvB,IAAIJ,aAAaK,MAAM,GAAG,GAAG;QAC3B,IAAIJ,mBAAmB,QAAQ;YAC7BG,OAAO;gBAAC;gBAAO;aAAU;YACzB,IAAID,iBAAiBC,KAAKE,IAAI,CAAC;QACjC,OAAO,IAAIL,mBAAmB,QAAQ;YACpCG,OAAO;gBAAC;gBAAO;aAAe;YAC9BA,KAAKE,IAAI,CAACH,kBAAkB,eAAe;QAC7C,OAAO;YACL,MAAM;YACNC,OAAO;gBAAC;gBAAW;aAAe;YAClCA,KAAKE,IAAI,CAACH,kBAAkB,eAAe;QAC7C;QAEAC,KAAKE,IAAI,IAAIN;IACf,OAAO;QACLI,OAAO;YAAC;SAAU,CAAC,4CAA4C;;QAE/D,IAAI,CAACF,UAAU;YACbE,KAAKE,IAAI,CAAC;YACVC,QAAQC,GAAG,CAACZ,OAAO;YACnB,IAAIK,mBAAmB,OAAO;gBAC5BM,QAAQC,GAAG,CACTZ,OAAO,CAAC,0BAA0B,EAAEK,eAAe,OAAO,CAAC;YAE/D;YACAM,QAAQC,GAAG;QACb;IACF;IAEA,OAAO,IAAIC,QAAQ,CAACC,SAASC;QAC3B;;KAEC,GACD,MAAMC,QAAQf,MAAMI,gBAAgBG,MAAM;YACxCS,KAAKd;YACLe,OAAO;YACPC,KAAK;gBACH,GAAGC,QAAQD,GAAG;gBACdE,SAAS;gBACT,mDAAmD;gBACnD,+BAA+B;gBAC/BC,UAAU;gBACVC,wBAAwB;YAC1B;QACF;QACAP,MAAMQ,EAAE,CAAC,SAAS,CAACC;YACjB,IAAIA,SAAS,GAAG;gBACdV,OAAO;oBAAEW,SAAS,GAAGrB,eAAe,CAAC,EAAEG,KAAKmB,IAAI,CAAC,MAAM;gBAAC;gBACxD;YACF;YACAb;QACF;IACF;AACF"}