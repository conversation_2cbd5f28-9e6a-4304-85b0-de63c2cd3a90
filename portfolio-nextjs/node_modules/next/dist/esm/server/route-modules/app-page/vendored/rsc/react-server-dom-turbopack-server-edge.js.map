{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactServerDOMTurbopackServerEdge\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMTurbopackServerEdge"], "mappings": "AAAAA,OAAOC,OAAO,GAAGC,QAAQ,yBAAyBC,QAAQ,CACxD,YACD,CAACC,iCAAiC"}