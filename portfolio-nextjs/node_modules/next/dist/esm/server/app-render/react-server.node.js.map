{"version": 3, "sources": ["../../../src/server/app-render/react-server.node.ts"], "sourcesContent": ["// This file should be opted into the react-server layer\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport {\n  createTemporaryReferenceSet,\n  decodeReply,\n  decodeReplyFromBusboy,\n  decodeAction,\n  decodeFormState,\n} from 'react-server-dom-webpack/server.node'\n"], "names": ["createTemporaryReferenceSet", "decodeReply", "decodeReplyFromBusboy", "decodeAction", "decodeFormState"], "mappings": "AAAA,wDAAwD;AAExD,6DAA6D;AAC7D,SACEA,2BAA2B,EAC3BC,WAAW,EACXC,qBAAqB,EACrBC,YAAY,EACZC,eAAe,QACV,uCAAsC"}