{"version": 3, "sources": ["../../../src/server/request/root-params.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n} from '../app-render/dynamic-rendering'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n} from '../app-render/work-unit-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FallbackRouteParams } from './fallback-params'\nimport type { Params } from './params'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nexport async function unstable_rootParams(): Promise<Params> {\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError('Missing workStore in unstable_rootParams')\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workUnitStore) {\n    throw new Error(\n      `Route ${workStore.route} used \\`unstable_rootParams()\\` in Pages Router. This API is only available within App Router.`\n    )\n  }\n\n  switch (workUnitStore.type) {\n    case 'unstable-cache':\n    case 'cache': {\n      throw new Error(\n        `Route ${workStore.route} used \\`unstable_rootParams()\\` inside \\`\"use cache\"\\` or \\`unstable_cache\\`. Support for this API inside cache scopes is planned for a future version of Next.js.`\n      )\n    }\n    case 'prerender':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      return createPrerenderRootParams(\n        workUnitStore.rootParams,\n        workStore,\n        workUnitStore\n      )\n    default:\n      return Promise.resolve(workUnitStore.rootParams)\n  }\n}\n\nfunction createPrerenderRootParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<Params> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams) {\n    let hasSomeFallbackParams = false\n    for (const key in underlyingParams) {\n      if (fallbackParams.has(key)) {\n        hasSomeFallbackParams = true\n        break\n      }\n    }\n\n    if (hasSomeFallbackParams) {\n      // params need to be treated as dynamic because we have at least one fallback param\n      if (prerenderStore.type === 'prerender') {\n        // We are in a dynamicIO (PPR or otherwise) prerender\n        const cachedParams = CachedParams.get(underlyingParams)\n        if (cachedParams) {\n          return cachedParams\n        }\n\n        const promise = makeHangingPromise<Params>(\n          prerenderStore.renderSignal,\n          '`unstable_rootParams`'\n        )\n        CachedParams.set(underlyingParams, promise)\n\n        return promise\n      }\n      // remaining cases are prerender-ppr and prerender-legacy\n      // We aren't in a dynamicIO prerender but we do have fallback params at this\n      // level so we need to make an erroring params object which will postpone\n      // if you access the fallback params\n      return makeErroringRootParams(\n        underlyingParams,\n        fallbackParams,\n        workStore,\n        prerenderStore\n      )\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return Promise.resolve(underlyingParams)\n}\n\nfunction makeErroringRootParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess(\n              'unstable_rootParams',\n              prop\n            )\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n"], "names": ["InvariantError", "postponeWithTracking", "throwToInterruptStaticGeneration", "workAsyncStorage", "workUnitAsyncStorage", "makeHangingPromise", "describeStringPropertyAccess", "wellKnownProperties", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "unstable_rootParams", "workStore", "getStore", "workUnitStore", "Error", "route", "type", "createPrerenderRootParams", "rootParams", "Promise", "resolve", "underlyingParams", "prerenderStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "hasSomeFallbackParams", "key", "has", "cachedParams", "get", "promise", "renderSignal", "set", "makeErroringRootParams", "augmentedUnderlying", "Object", "keys", "for<PERSON>ach", "prop", "defineProperty", "expression", "dynamicTracking", "enumerable"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mCAAkC;AACjE,SACEC,oBAAoB,EACpBC,gCAAgC,QAC3B,kCAAiC;AACxC,SACEC,gBAAgB,QAEX,4CAA2C;AAClD,SACEC,oBAAoB,QAIf,iDAAgD;AACvD,SAASC,kBAAkB,QAAQ,6BAA4B;AAG/D,SACEC,4BAA4B,EAC5BC,mBAAmB,QACd,uCAAsC;AAG7C,MAAMC,eAAe,IAAIC;AAEzB,OAAO,eAAeC;IACpB,MAAMC,YAAYR,iBAAiBS,QAAQ;IAC3C,IAAI,CAACD,WAAW;QACd,MAAM,qBAA8D,CAA9D,IAAIX,eAAe,6CAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;IACrE;IAEA,MAAMa,gBAAgBT,qBAAqBQ,QAAQ;IAEnD,IAAI,CAACC,eAAe;QAClB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,MAAM,EAAEH,UAAUI,KAAK,CAAC,8FAA8F,CAAC,GADpH,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAQF,cAAcG,IAAI;QACxB,KAAK;QACL,KAAK;YAAS;gBACZ,MAAM,qBAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAEH,UAAUI,KAAK,CAAC,kKAAkK,CAAC,GADxL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACA,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOE,0BACLJ,cAAcK,UAAU,EACxBP,WACAE;QAEJ;YACE,OAAOM,QAAQC,OAAO,CAACP,cAAcK,UAAU;IACnD;AACF;AAEA,SAASD,0BACPI,gBAAwB,EACxBV,SAAoB,EACpBW,cAA8B;IAE9B,MAAMC,iBAAiBZ,UAAUa,mBAAmB;IACpD,IAAID,gBAAgB;QAClB,IAAIE,wBAAwB;QAC5B,IAAK,MAAMC,OAAOL,iBAAkB;YAClC,IAAIE,eAAeI,GAAG,CAACD,MAAM;gBAC3BD,wBAAwB;gBACxB;YACF;QACF;QAEA,IAAIA,uBAAuB;YACzB,mFAAmF;YACnF,IAAIH,eAAeN,IAAI,KAAK,aAAa;gBACvC,qDAAqD;gBACrD,MAAMY,eAAepB,aAAaqB,GAAG,CAACR;gBACtC,IAAIO,cAAc;oBAChB,OAAOA;gBACT;gBAEA,MAAME,UAAUzB,mBACdiB,eAAeS,YAAY,EAC3B;gBAEFvB,aAAawB,GAAG,CAACX,kBAAkBS;gBAEnC,OAAOA;YACT;YACA,yDAAyD;YACzD,4EAA4E;YAC5E,yEAAyE;YACzE,oCAAoC;YACpC,OAAOG,uBACLZ,kBACAE,gBACAZ,WACAW;QAEJ;IACF;IAEA,qFAAqF;IACrF,OAAOH,QAAQC,OAAO,CAACC;AACzB;AAEA,SAASY,uBACPZ,gBAAwB,EACxBE,cAAmC,EACnCZ,SAAoB,EACpBW,cAAwD;IAExD,MAAMM,eAAepB,aAAaqB,GAAG,CAACR;IACtC,IAAIO,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMM,sBAAsB;QAAE,GAAGb,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMS,UAAUX,QAAQC,OAAO,CAACc;IAChC1B,aAAawB,GAAG,CAACX,kBAAkBS;IAEnCK,OAAOC,IAAI,CAACf,kBAAkBgB,OAAO,CAAC,CAACC;QACrC,IAAI/B,oBAAoBoB,GAAG,CAACW,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAIf,eAAeI,GAAG,CAACW,OAAO;gBAC5BH,OAAOI,cAAc,CAACL,qBAAqBI,MAAM;oBAC/CT;wBACE,MAAMW,aAAalC,6BACjB,uBACAgC;wBAEF,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,qEAAqE;wBACrE,iCAAiC;wBACjC,IAAIhB,eAAeN,IAAI,KAAK,iBAAiB;4BAC3C,+BAA+B;4BAC/Bf,qBACEU,UAAUI,KAAK,EACfyB,YACAlB,eAAemB,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBvC,iCACEsC,YACA7B,WACAW;wBAEJ;oBACF;oBACAoB,YAAY;gBACd;YACF,OAAO;;gBACHZ,OAAe,CAACQ,KAAK,GAAGjB,gBAAgB,CAACiB,KAAK;YAClD;QACF;IACF;IAEA,OAAOR;AACT"}