{"version": 3, "sources": ["../../src/server/load-components.ts"], "sourcesContent": ["import type {\n  AppType,\n  DocumentType,\n  NextComponentType,\n} from '../shared/lib/utils'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type {\n  PageConfig,\n  GetStaticPaths,\n  GetServerSideProps,\n  GetStaticProps,\n} from '../types'\nimport type { RouteModule } from './route-modules/route-module'\nimport type { BuildManifest } from './get-page-files'\nimport type { ActionManifest } from '../build/webpack/plugins/flight-client-entry-plugin'\n\nimport {\n  BUILD_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  CLIENT_REFERENCE_MANIFEST,\n  SERVER_REFERENCE_MANIFEST,\n  DYNAMIC_CSS_MANIFEST,\n  SUBRESOURCE_INTEGRITY_MANIFEST,\n} from '../shared/lib/constants'\nimport { join } from 'path'\nimport { requirePage } from './require'\nimport { interopDefault } from '../lib/interop-default'\nimport { getTracer } from './lib/trace/tracer'\nimport { LoadComponentsSpan } from './lib/trace/constants'\nimport { evalManifest, loadManifest } from './load-manifest'\nimport { wait } from '../lib/wait'\nimport { setReferenceManifestsSingleton } from './app-render/encryption-utils'\nimport { createServerModuleMap } from './app-render/action-utils'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { isStaticMetadataRoute } from '../lib/metadata/is-metadata-route'\n\nexport type ManifestItem = {\n  id: number | string\n  files: string[]\n}\n\nexport type ReactLoadableManifest = { [moduleId: string]: ManifestItem }\n/**\n * This manifest prevents removing server rendered <link> tags after client\n * navigation. This is only needed under `Pages dir && Production && Webpack`.\n * @see https://github.com/vercel/next.js/pull/72959\n */\nexport type DynamicCssManifest = string[]\n\n/**\n * A manifest entry type for the react-loadable-manifest.json.\n *\n * The whole manifest.json is a type of `Record<pathname, LoadableManifest>`\n * where pathname is a string-based key points to the path of the page contains\n * each dynamic imports.\n */\nexport interface LoadableManifest {\n  [k: string]: { id: string | number; files: string[] }\n}\n\nexport type LoadComponentsReturnType<NextModule = any> = {\n  Component: NextComponentType\n  pageConfig: PageConfig\n  buildManifest: DeepReadonly<BuildManifest>\n  subresourceIntegrityManifest?: DeepReadonly<Record<string, string>>\n  reactLoadableManifest: DeepReadonly<ReactLoadableManifest>\n  dynamicCssManifest?: DeepReadonly<DynamicCssManifest>\n  clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  serverActionsManifest?: any\n  Document: DocumentType\n  App: AppType\n  getStaticProps?: GetStaticProps\n  getStaticPaths?: GetStaticPaths\n  getServerSideProps?: GetServerSideProps\n  ComponentMod: NextModule\n  routeModule: RouteModule\n  isAppPath?: boolean\n  page: string\n  multiZoneDraftMode?: boolean\n}\n\n/**\n * Load manifest file with retries, defaults to 3 attempts.\n */\nexport async function loadManifestWithRetries<T extends object>(\n  manifestPath: string,\n  attempts = 3\n) {\n  while (true) {\n    try {\n      return loadManifest<T>(manifestPath)\n    } catch (err) {\n      attempts--\n      if (attempts <= 0) throw err\n\n      await wait(100)\n    }\n  }\n}\n\n/**\n * Load manifest file with retries, defaults to 3 attempts, or return undefined.\n */\nexport async function tryLoadManifestWithRetries<T extends object>(\n  manifestPath: string,\n  attempts = 3\n) {\n  try {\n    return await loadManifestWithRetries<T>(manifestPath, attempts)\n  } catch (err) {\n    return undefined\n  }\n}\n\n/**\n * Load manifest file with retries, defaults to 3 attempts.\n */\nexport async function evalManifestWithRetries<T extends object>(\n  manifestPath: string,\n  attempts = 3\n) {\n  while (true) {\n    try {\n      return evalManifest<T>(manifestPath)\n    } catch (err) {\n      attempts--\n      if (attempts <= 0) throw err\n\n      await wait(100)\n    }\n  }\n}\n\nasync function tryLoadClientReferenceManifest(\n  manifestPath: string,\n  entryName: string,\n  attempts?: number\n) {\n  try {\n    const context = await evalManifestWithRetries<{\n      __RSC_MANIFEST: { [key: string]: ClientReferenceManifest }\n    }>(manifestPath, attempts)\n    return context.__RSC_MANIFEST[entryName]\n  } catch (err) {\n    return undefined\n  }\n}\n\nasync function loadComponentsImpl<N = any>({\n  distDir,\n  page,\n  isAppPath,\n  isDev,\n  sriEnabled,\n}: {\n  distDir: string\n  page: string\n  isAppPath: boolean\n  isDev: boolean\n  sriEnabled: boolean\n}): Promise<LoadComponentsReturnType<N>> {\n  let DocumentMod = {}\n  let AppMod = {}\n  if (!isAppPath) {\n    ;[DocumentMod, AppMod] = await Promise.all([\n      requirePage('/_document', distDir, false),\n      requirePage('/_app', distDir, false),\n    ])\n  }\n\n  // In dev mode we retry loading a manifest file to handle a race condition\n  // that can occur while app and pages are compiling at the same time, and the\n  // build-manifest is still being written to disk while an app path is\n  // attempting to load.\n  const manifestLoadAttempts = isDev ? 3 : 1\n\n  let reactLoadableManifestPath\n  if (!process.env.TURBOPACK) {\n    reactLoadableManifestPath = join(distDir, REACT_LOADABLE_MANIFEST)\n  } else if (isAppPath) {\n    reactLoadableManifestPath = join(\n      distDir,\n      'server',\n      'app',\n      page,\n      REACT_LOADABLE_MANIFEST\n    )\n  } else {\n    reactLoadableManifestPath = join(\n      distDir,\n      'server',\n      'pages',\n      normalizePagePath(page),\n      REACT_LOADABLE_MANIFEST\n    )\n  }\n\n  // Make sure to avoid loading the manifest for static metadata routes for better performance.\n  const hasClientManifest = !isStaticMetadataRoute(page)\n\n  // Load the manifest files first\n  //\n  // Loading page-specific manifests shouldn't throw an error if the manifest couldn't be found, so\n  // that the `requirePage` call below will throw the correct error in that case\n  // (a `PageNotFoundError`).\n  const [\n    buildManifest,\n    reactLoadableManifest,\n    dynamicCssManifest,\n    clientReferenceManifest,\n    serverActionsManifest,\n    subresourceIntegrityManifest,\n  ] = await Promise.all([\n    loadManifestWithRetries<BuildManifest>(\n      join(distDir, BUILD_MANIFEST),\n      manifestLoadAttempts\n    ),\n    tryLoadManifestWithRetries<ReactLoadableManifest>(\n      reactLoadableManifestPath,\n      manifestLoadAttempts\n    ),\n    // This manifest will only exist in Pages dir && Production && Webpack.\n    isAppPath || process.env.TURBOPACK\n      ? undefined\n      : loadManifestWithRetries<DynamicCssManifest>(\n          join(distDir, `${DYNAMIC_CSS_MANIFEST}.json`),\n          manifestLoadAttempts\n        ).catch(() => undefined),\n    isAppPath && hasClientManifest\n      ? tryLoadClientReferenceManifest(\n          join(\n            distDir,\n            'server',\n            'app',\n            page.replace(/%5F/g, '_') + '_' + CLIENT_REFERENCE_MANIFEST + '.js'\n          ),\n          page.replace(/%5F/g, '_'),\n          manifestLoadAttempts\n        )\n      : undefined,\n    isAppPath\n      ? loadManifestWithRetries<ActionManifest>(\n          join(distDir, 'server', SERVER_REFERENCE_MANIFEST + '.json'),\n          manifestLoadAttempts\n        ).catch(() => null)\n      : null,\n    sriEnabled\n      ? loadManifestWithRetries<DeepReadonly<Record<string, string>>>(\n          join(distDir, 'server', SUBRESOURCE_INTEGRITY_MANIFEST + '.json')\n        ).catch(() => undefined)\n      : undefined,\n  ])\n\n  // Before requiring the actual page module, we have to set the reference\n  // manifests to our global store so Server Action's encryption util can access\n  // to them at the top level of the page module.\n  if (serverActionsManifest && clientReferenceManifest) {\n    setReferenceManifestsSingleton({\n      page,\n      clientReferenceManifest,\n      serverActionsManifest,\n      serverModuleMap: createServerModuleMap({\n        serverActionsManifest,\n      }),\n    })\n  }\n\n  const ComponentMod = await requirePage(page, distDir, isAppPath)\n\n  const Component = interopDefault(ComponentMod)\n  const Document = interopDefault(DocumentMod)\n  const App = interopDefault(AppMod)\n\n  const { getServerSideProps, getStaticProps, getStaticPaths, routeModule } =\n    ComponentMod\n\n  return {\n    App,\n    Document,\n    Component,\n    buildManifest,\n    subresourceIntegrityManifest,\n    reactLoadableManifest: reactLoadableManifest || {},\n    dynamicCssManifest,\n    pageConfig: ComponentMod.config || {},\n    ComponentMod,\n    getServerSideProps,\n    getStaticProps,\n    getStaticPaths,\n    clientReferenceManifest,\n    serverActionsManifest,\n    isAppPath,\n    page,\n    routeModule,\n  }\n}\n\nexport const loadComponents = getTracer().wrap(\n  LoadComponentsSpan.loadComponents,\n  loadComponentsImpl\n)\n"], "names": ["BUILD_MANIFEST", "REACT_LOADABLE_MANIFEST", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "DYNAMIC_CSS_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "join", "requirePage", "interopDefault", "getTracer", "LoadComponentsSpan", "evalManifest", "loadManifest", "wait", "setReferenceManifestsSingleton", "createServerModuleMap", "normalizePagePath", "isStaticMetadataRoute", "loadManifestWithRetries", "manifestPath", "attempts", "err", "tryLoadManifestWithRetries", "undefined", "evalManifestWithRetries", "tryLoadClientReferenceManifest", "entryName", "context", "__RSC_MANIFEST", "loadComponentsImpl", "distDir", "page", "isAppPath", "isDev", "sriEnabled", "DocumentMod", "AppMod", "Promise", "all", "manifestLoadAttempts", "reactLoadableManifestPath", "process", "env", "TURBOPACK", "hasClientManifest", "buildManifest", "reactLoadableManifest", "dynamicCssManifest", "clientReferenceManifest", "serverActionsManifest", "subresourceIntegrityManifest", "catch", "replace", "serverModuleMap", "ComponentMod", "Component", "Document", "App", "getServerSideProps", "getStaticProps", "getStaticPaths", "routeModule", "pageConfig", "config", "loadComponents", "wrap"], "mappings": "AAgBA,SACEA,cAAc,EACdC,uBAAuB,EACvBC,yBAAyB,EACzBC,yBAAyB,EACzBC,oBAAoB,EACpBC,8BAA8B,QACzB,0BAAyB;AAChC,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,WAAW,QAAQ,YAAW;AACvC,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,SAAS,QAAQ,qBAAoB;AAC9C,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,YAAY,EAAEC,YAAY,QAAQ,kBAAiB;AAC5D,SAASC,IAAI,QAAQ,cAAa;AAClC,SAASC,8BAA8B,QAAQ,gCAA+B;AAC9E,SAASC,qBAAqB,QAAQ,4BAA2B;AAEjE,SAASC,iBAAiB,QAAQ,8CAA6C;AAC/E,SAASC,qBAAqB,QAAQ,oCAAmC;AA+CzE;;CAEC,GACD,OAAO,eAAeC,wBACpBC,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOR,aAAgBO;QACzB,EAAE,OAAOE,KAAK;YACZD;YACA,IAAIA,YAAY,GAAG,MAAMC;YAEzB,MAAMR,KAAK;QACb;IACF;AACF;AAEA;;CAEC,GACD,OAAO,eAAeS,2BACpBH,YAAoB,EACpBC,WAAW,CAAC;IAEZ,IAAI;QACF,OAAO,MAAMF,wBAA2BC,cAAcC;IACxD,EAAE,OAAOC,KAAK;QACZ,OAAOE;IACT;AACF;AAEA;;CAEC,GACD,OAAO,eAAeC,wBACpBL,YAAoB,EACpBC,WAAW,CAAC;IAEZ,MAAO,KAAM;QACX,IAAI;YACF,OAAOT,aAAgBQ;QACzB,EAAE,OAAOE,KAAK;YACZD;YACA,IAAIA,YAAY,GAAG,MAAMC;YAEzB,MAAMR,KAAK;QACb;IACF;AACF;AAEA,eAAeY,+BACbN,YAAoB,EACpBO,SAAiB,EACjBN,QAAiB;IAEjB,IAAI;QACF,MAAMO,UAAU,MAAMH,wBAEnBL,cAAcC;QACjB,OAAOO,QAAQC,cAAc,CAACF,UAAU;IAC1C,EAAE,OAAOL,KAAK;QACZ,OAAOE;IACT;AACF;AAEA,eAAeM,mBAA4B,EACzCC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,UAAU,EAOX;IACC,IAAIC,cAAc,CAAC;IACnB,IAAIC,SAAS,CAAC;IACd,IAAI,CAACJ,WAAW;;QACb,CAACG,aAAaC,OAAO,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACzC/B,YAAY,cAAcuB,SAAS;YACnCvB,YAAY,SAASuB,SAAS;SAC/B;IACH;IAEA,0EAA0E;IAC1E,6EAA6E;IAC7E,qEAAqE;IACrE,sBAAsB;IACtB,MAAMS,uBAAuBN,QAAQ,IAAI;IAEzC,IAAIO;IACJ,IAAI,CAACC,QAAQC,GAAG,CAACC,SAAS,EAAE;QAC1BH,4BAA4BlC,KAAKwB,SAAS7B;IAC5C,OAAO,IAAI+B,WAAW;QACpBQ,4BAA4BlC,KAC1BwB,SACA,UACA,OACAC,MACA9B;IAEJ,OAAO;QACLuC,4BAA4BlC,KAC1BwB,SACA,UACA,SACAd,kBAAkBe,OAClB9B;IAEJ;IAEA,6FAA6F;IAC7F,MAAM2C,oBAAoB,CAAC3B,sBAAsBc;IAEjD,gCAAgC;IAChC,EAAE;IACF,iGAAiG;IACjG,8EAA8E;IAC9E,2BAA2B;IAC3B,MAAM,CACJc,eACAC,uBACAC,oBACAC,yBACAC,uBACAC,6BACD,GAAG,MAAMb,QAAQC,GAAG,CAAC;QACpBpB,wBACEZ,KAAKwB,SAAS9B,iBACduC;QAEFjB,2BACEkB,2BACAD;QAEF,uEAAuE;QACvEP,aAAaS,QAAQC,GAAG,CAACC,SAAS,GAC9BpB,YACAL,wBACEZ,KAAKwB,SAAS,GAAG1B,qBAAqB,KAAK,CAAC,GAC5CmC,sBACAY,KAAK,CAAC,IAAM5B;QAClBS,aAAaY,oBACTnB,+BACEnB,KACEwB,SACA,UACA,OACAC,KAAKqB,OAAO,CAAC,QAAQ,OAAO,MAAMlD,4BAA4B,QAEhE6B,KAAKqB,OAAO,CAAC,QAAQ,MACrBb,wBAEFhB;QACJS,YACId,wBACEZ,KAAKwB,SAAS,UAAU3B,4BAA4B,UACpDoC,sBACAY,KAAK,CAAC,IAAM,QACd;QACJjB,aACIhB,wBACEZ,KAAKwB,SAAS,UAAUzB,iCAAiC,UACzD8C,KAAK,CAAC,IAAM5B,aACdA;KACL;IAED,wEAAwE;IACxE,8EAA8E;IAC9E,+CAA+C;IAC/C,IAAI0B,yBAAyBD,yBAAyB;QACpDlC,+BAA+B;YAC7BiB;YACAiB;YACAC;YACAI,iBAAiBtC,sBAAsB;gBACrCkC;YACF;QACF;IACF;IAEA,MAAMK,eAAe,MAAM/C,YAAYwB,MAAMD,SAASE;IAEtD,MAAMuB,YAAY/C,eAAe8C;IACjC,MAAME,WAAWhD,eAAe2B;IAChC,MAAMsB,MAAMjD,eAAe4B;IAE3B,MAAM,EAAEsB,kBAAkB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,WAAW,EAAE,GACvEP;IAEF,OAAO;QACLG;QACAD;QACAD;QACAV;QACAK;QACAJ,uBAAuBA,yBAAyB,CAAC;QACjDC;QACAe,YAAYR,aAAaS,MAAM,IAAI,CAAC;QACpCT;QACAI;QACAC;QACAC;QACAZ;QACAC;QACAjB;QACAD;QACA8B;IACF;AACF;AAEA,OAAO,MAAMG,iBAAiBvD,YAAYwD,IAAI,CAC5CvD,mBAAmBsD,cAAc,EACjCnC,oBACD"}