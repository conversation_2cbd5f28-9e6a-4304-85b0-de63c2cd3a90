{"version": 3, "sources": ["../../../src/server/lib/patch-fetch.ts"], "sourcesContent": ["import type {\n  WorkAsyncStorage,\n  WorkStore,\n} from '../app-render/work-async-storage.external'\n\nimport { AppRenderSpan, NextNodeServerSpan } from './trace/constants'\nimport { getTracer, SpanKind } from './trace/tracer'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  NEXT_CACHE_TAG_MAX_ITEMS,\n  NEXT_CACHE_TAG_MAX_LENGTH,\n} from '../../lib/constants'\nimport { markCurrentScopeAsDynamic } from '../app-render/dynamic-rendering'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FetchMetric } from '../base-http'\nimport { createDedupeFetch } from './dedupe-fetch'\nimport type { WorkUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../response-cache'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport { cloneResponse } from './clone-response'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\ntype Fetcher = typeof fetch\n\ntype PatchedFetcher = Fetcher & {\n  readonly __nextPatched: true\n  readonly __nextGetStaticStore: () => WorkAsyncStorage\n  readonly _nextOriginalFetch: Fetcher\n}\n\nexport const NEXT_PATCH_SYMBOL = Symbol.for('next-patch')\n\nfunction isFetchPatched() {\n  return (globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] === true\n}\n\nexport function validateRevalidate(\n  revalidateVal: unknown,\n  route: string\n): undefined | number {\n  try {\n    let normalizedRevalidate: number | undefined = undefined\n\n    if (revalidateVal === false) {\n      normalizedRevalidate = INFINITE_CACHE\n    } else if (\n      typeof revalidateVal === 'number' &&\n      !isNaN(revalidateVal) &&\n      revalidateVal > -1\n    ) {\n      normalizedRevalidate = revalidateVal\n    } else if (typeof revalidateVal !== 'undefined') {\n      throw new Error(\n        `Invalid revalidate value \"${revalidateVal}\" on \"${route}\", must be a non-negative number or false`\n      )\n    }\n    return normalizedRevalidate\n  } catch (err: any) {\n    // handle client component error from attempting to check revalidate value\n    if (err instanceof Error && err.message.includes('Invalid revalidate')) {\n      throw err\n    }\n    return undefined\n  }\n}\n\nexport function validateTags(tags: any[], description: string) {\n  const validTags: string[] = []\n  const invalidTags: Array<{\n    tag: any\n    reason: string\n  }> = []\n\n  for (let i = 0; i < tags.length; i++) {\n    const tag = tags[i]\n\n    if (typeof tag !== 'string') {\n      invalidTags.push({ tag, reason: 'invalid type, must be a string' })\n    } else if (tag.length > NEXT_CACHE_TAG_MAX_LENGTH) {\n      invalidTags.push({\n        tag,\n        reason: `exceeded max length of ${NEXT_CACHE_TAG_MAX_LENGTH}`,\n      })\n    } else {\n      validTags.push(tag)\n    }\n\n    if (validTags.length > NEXT_CACHE_TAG_MAX_ITEMS) {\n      console.warn(\n        `Warning: exceeded max tag count for ${description}, dropped tags:`,\n        tags.slice(i).join(', ')\n      )\n      break\n    }\n  }\n\n  if (invalidTags.length > 0) {\n    console.warn(`Warning: invalid tags passed to ${description}: `)\n\n    for (const { tag, reason } of invalidTags) {\n      console.log(`tag: \"${tag}\" ${reason}`)\n    }\n  }\n  return validTags\n}\n\nfunction trackFetchMetric(\n  workStore: WorkStore,\n  ctx: Omit<FetchMetric, 'end' | 'idx'>\n) {\n  // If the static generation store is not available, we can't track the fetch\n  if (!workStore) return\n  if (workStore.requestEndedState?.ended) return\n\n  const isDebugBuild =\n    (!!process.env.NEXT_DEBUG_BUILD ||\n      process.env.NEXT_SSG_FETCH_METRICS === '1') &&\n    workStore.isStaticGeneration\n  const isDevelopment = process.env.NODE_ENV === 'development'\n\n  if (\n    // The only time we want to track fetch metrics outside of development is when\n    // we are performing a static generation & we are in debug mode.\n    !isDebugBuild &&\n    !isDevelopment\n  ) {\n    return\n  }\n\n  workStore.fetchMetrics ??= []\n\n  workStore.fetchMetrics.push({\n    ...ctx,\n    end: performance.timeOrigin + performance.now(),\n    idx: workStore.nextFetchId || 0,\n  })\n}\n\ninterface PatchableModule {\n  workAsyncStorage: WorkAsyncStorage\n  workUnitAsyncStorage: WorkUnitAsyncStorage\n}\n\nexport function createPatchedFetcher(\n  originFetch: Fetcher,\n  { workAsyncStorage, workUnitAsyncStorage }: PatchableModule\n): PatchedFetcher {\n  // Create the patched fetch function. We don't set the type here, as it's\n  // verified as the return value of this function.\n  const patched = async (\n    input: RequestInfo | URL,\n    init: RequestInit | undefined\n  ) => {\n    let url: URL | undefined\n    try {\n      url = new URL(input instanceof Request ? input.url : input)\n      url.username = ''\n      url.password = ''\n    } catch {\n      // Error caused by malformed URL should be handled by native fetch\n      url = undefined\n    }\n    const fetchUrl = url?.href ?? ''\n    const method = init?.method?.toUpperCase() || 'GET'\n\n    // Do create a new span trace for internal fetches in the\n    // non-verbose mode.\n    const isInternal = (init?.next as any)?.internal === true\n    const hideSpan = process.env.NEXT_OTEL_FETCH_DISABLED === '1'\n    // We don't track fetch metrics for internal fetches\n    // so it's not critical that we have a start time, as it won't be recorded.\n    // This is to workaround a flaky issue where performance APIs might\n    // not be available and will require follow-up investigation.\n    const fetchStart: number | undefined = isInternal\n      ? undefined\n      : performance.timeOrigin + performance.now()\n\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // During static generation we track cache reads so we can reason about when they fill\n    let cacheSignal =\n      workUnitStore && workUnitStore.type === 'prerender'\n        ? workUnitStore.cacheSignal\n        : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n\n    const result = getTracer().trace(\n      isInternal ? NextNodeServerSpan.internalFetch : AppRenderSpan.fetch,\n      {\n        hideSpan,\n        kind: SpanKind.CLIENT,\n        spanName: ['fetch', method, fetchUrl].filter(Boolean).join(' '),\n        attributes: {\n          'http.url': fetchUrl,\n          'http.method': method,\n          'net.peer.name': url?.hostname,\n          'net.peer.port': url?.port || undefined,\n        },\n      },\n      async () => {\n        // If this is an internal fetch, we should not do any special treatment.\n        if (isInternal) {\n          return originFetch(input, init)\n        }\n\n        // If the workStore is not available, we can't do any\n        // special treatment of fetch, therefore fallback to the original\n        // fetch implementation.\n        if (!workStore) {\n          return originFetch(input, init)\n        }\n\n        // We should also fallback to the original fetch implementation if we\n        // are in draft mode, it does not constitute a static generation.\n        if (workStore.isDraftMode) {\n          return originFetch(input, init)\n        }\n\n        const isRequestInput =\n          input &&\n          typeof input === 'object' &&\n          typeof (input as Request).method === 'string'\n\n        const getRequestMeta = (field: string) => {\n          // If request input is present but init is not, retrieve from input first.\n          const value = (init as any)?.[field]\n          return value || (isRequestInput ? (input as any)[field] : null)\n        }\n\n        let finalRevalidate: number | undefined = undefined\n        const getNextField = (field: 'revalidate' | 'tags') => {\n          return typeof init?.next?.[field] !== 'undefined'\n            ? init?.next?.[field]\n            : isRequestInput\n              ? (input as any).next?.[field]\n              : undefined\n        }\n        // RequestInit doesn't keep extra fields e.g. next so it's\n        // only available if init is used separate\n        let currentFetchRevalidate = getNextField('revalidate')\n        const tags: string[] = validateTags(\n          getNextField('tags') || [],\n          `fetch ${input.toString()}`\n        )\n\n        const revalidateStore =\n          workUnitStore &&\n          (workUnitStore.type === 'cache' ||\n            workUnitStore.type === 'prerender' ||\n            workUnitStore.type === 'prerender-ppr' ||\n            workUnitStore.type === 'prerender-legacy')\n            ? workUnitStore\n            : undefined\n\n        if (revalidateStore) {\n          if (Array.isArray(tags)) {\n            // Collect tags onto parent caches or parent prerenders.\n            const collectedTags =\n              revalidateStore.tags ?? (revalidateStore.tags = [])\n            for (const tag of tags) {\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const implicitTags = workUnitStore?.implicitTags\n\n        // Inside unstable-cache we treat it the same as force-no-store on the\n        // page.\n        const pageFetchCacheMode =\n          workUnitStore && workUnitStore.type === 'unstable-cache'\n            ? 'force-no-store'\n            : workStore.fetchCache\n\n        const isUsingNoStore = !!workStore.isUnstableNoStore\n\n        let currentFetchCacheConfig = getRequestMeta('cache')\n        let cacheReason = ''\n        let cacheWarning: string | undefined\n\n        if (\n          typeof currentFetchCacheConfig === 'string' &&\n          typeof currentFetchRevalidate !== 'undefined'\n        ) {\n          // If the revalidate value conflicts with the cache value, we should warn the user and unset the conflicting values.\n          const isConflictingRevalidate =\n            // revalidate: 0 and cache: force-cache\n            (currentFetchCacheConfig === 'force-cache' &&\n              currentFetchRevalidate === 0) ||\n            // revalidate: >0 or revalidate: false and cache: no-store\n            (currentFetchCacheConfig === 'no-store' &&\n              (currentFetchRevalidate > 0 || currentFetchRevalidate === false))\n\n          if (isConflictingRevalidate) {\n            cacheWarning = `Specified \"cache: ${currentFetchCacheConfig}\" and \"revalidate: ${currentFetchRevalidate}\", only one should be specified.`\n            currentFetchCacheConfig = undefined\n            currentFetchRevalidate = undefined\n          }\n        }\n\n        const hasExplicitFetchCacheOptOut =\n          // fetch config itself signals not to cache\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store' ||\n          // the fetch isn't explicitly caching and the segment level cache config signals not to cache\n          // note: `pageFetchCacheMode` is also set by being in an unstable_cache context.\n          pageFetchCacheMode === 'force-no-store' ||\n          pageFetchCacheMode === 'only-no-store'\n\n        // If no explicit fetch cache mode is set, but dynamic = `force-dynamic` is set,\n        // we shouldn't consider caching the fetch. This is because the `dynamic` cache\n        // is considered a \"top-level\" cache mode, whereas something like `fetchCache` is more\n        // fine-grained. Top-level modes are responsible for setting reasonable defaults for the\n        // other configurations.\n        const noFetchConfigAndForceDynamic =\n          !pageFetchCacheMode &&\n          !currentFetchCacheConfig &&\n          !currentFetchRevalidate &&\n          workStore.forceDynamic\n\n        if (\n          // force-cache was specified without a revalidate value. We set the revalidate value to false\n          // which will signal the cache to not revalidate\n          currentFetchCacheConfig === 'force-cache' &&\n          typeof currentFetchRevalidate === 'undefined'\n        ) {\n          currentFetchRevalidate = false\n        } else if (\n          // if we are inside of \"use cache\"/\"unstable_cache\"\n          // we shouldn't set the revalidate to 0 as it's overridden\n          // by the cache context\n          workUnitStore?.type !== 'cache' &&\n          (hasExplicitFetchCacheOptOut || noFetchConfigAndForceDynamic)\n        ) {\n          currentFetchRevalidate = 0\n        }\n\n        if (\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store'\n        ) {\n          cacheReason = `cache: ${currentFetchCacheConfig}`\n        }\n\n        finalRevalidate = validateRevalidate(\n          currentFetchRevalidate,\n          workStore.route\n        )\n\n        const _headers = getRequestMeta('headers')\n        const initHeaders: Headers =\n          typeof _headers?.get === 'function'\n            ? _headers\n            : new Headers(_headers || {})\n\n        const hasUnCacheableHeader =\n          initHeaders.get('authorization') || initHeaders.get('cookie')\n\n        const isUnCacheableMethod = !['get', 'head'].includes(\n          getRequestMeta('method')?.toLowerCase() || 'get'\n        )\n\n        /**\n         * We automatically disable fetch caching under the following conditions:\n         * - Fetch cache configs are not set. Specifically:\n         *    - A page fetch cache mode is not set (export const fetchCache=...)\n         *    - A fetch cache mode is not set in the fetch call (fetch(url, { cache: ... }))\n         *      or the fetch cache mode is set to 'default'\n         *    - A fetch revalidate value is not set in the fetch call (fetch(url, { revalidate: ... }))\n         * - OR the fetch comes after a configuration that triggered dynamic rendering (e.g., reading cookies())\n         *   and the fetch was considered uncacheable (e.g., POST method or has authorization headers)\n         */\n        const hasNoExplicitCacheConfig =\n          // eslint-disable-next-line eqeqeq\n          pageFetchCacheMode == undefined &&\n          // eslint-disable-next-line eqeqeq\n          (currentFetchCacheConfig == undefined ||\n            // when considering whether to opt into the default \"no-cache\" fetch semantics,\n            // a \"default\" cache config should be treated the same as no cache config\n            currentFetchCacheConfig === 'default') &&\n          // eslint-disable-next-line eqeqeq\n          currentFetchRevalidate == undefined\n        const autoNoCache =\n          // this condition is hit for null/undefined\n          // eslint-disable-next-line eqeqeq\n          (hasNoExplicitCacheConfig &&\n            // we disable automatic no caching behavior during build time SSG so that we can still\n            // leverage the fetch cache between SSG workers\n            !workStore.isPrerendering) ||\n          ((hasUnCacheableHeader || isUnCacheableMethod) &&\n            revalidateStore &&\n            revalidateStore.revalidate === 0)\n\n        if (\n          hasNoExplicitCacheConfig &&\n          workUnitStore !== undefined &&\n          workUnitStore.type === 'prerender'\n        ) {\n          // If we have no cache config, and we're in Dynamic I/O prerendering, it'll be a dynamic call.\n          // We don't have to issue that dynamic call.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n            cacheSignal = null\n          }\n          return makeHangingPromise<Response>(\n            workUnitStore.renderSignal,\n            'fetch()'\n          )\n        }\n\n        switch (pageFetchCacheMode) {\n          case 'force-no-store': {\n            cacheReason = 'fetchCache = force-no-store'\n            break\n          }\n          case 'only-no-store': {\n            if (\n              currentFetchCacheConfig === 'force-cache' ||\n              (typeof finalRevalidate !== 'undefined' && finalRevalidate > 0)\n            ) {\n              throw new Error(\n                `cache: 'force-cache' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-no-store'`\n              )\n            }\n            cacheReason = 'fetchCache = only-no-store'\n            break\n          }\n          case 'only-cache': {\n            if (currentFetchCacheConfig === 'no-store') {\n              throw new Error(\n                `cache: 'no-store' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-cache'`\n              )\n            }\n            break\n          }\n          case 'force-cache': {\n            if (\n              typeof currentFetchRevalidate === 'undefined' ||\n              currentFetchRevalidate === 0\n            ) {\n              cacheReason = 'fetchCache = force-cache'\n              finalRevalidate = INFINITE_CACHE\n            }\n            break\n          }\n          default:\n          // sometimes we won't match the above cases. the reason we don't move\n          // everything to this switch is the use of autoNoCache which is not a fetchCacheMode\n          // I suspect this could be unified with fetchCacheMode however in which case we could\n          // simplify the switch case and ensure we have an exhaustive switch handling all modes\n        }\n\n        if (typeof finalRevalidate === 'undefined') {\n          if (pageFetchCacheMode === 'default-cache' && !isUsingNoStore) {\n            finalRevalidate = INFINITE_CACHE\n            cacheReason = 'fetchCache = default-cache'\n          } else if (pageFetchCacheMode === 'default-no-store') {\n            finalRevalidate = 0\n            cacheReason = 'fetchCache = default-no-store'\n          } else if (isUsingNoStore) {\n            finalRevalidate = 0\n            cacheReason = 'noStore call'\n          } else if (autoNoCache) {\n            finalRevalidate = 0\n            cacheReason = 'auto no cache'\n          } else {\n            // TODO: should we consider this case an invariant?\n            cacheReason = 'auto cache'\n            finalRevalidate = revalidateStore\n              ? revalidateStore.revalidate\n              : INFINITE_CACHE\n          }\n        } else if (!cacheReason) {\n          cacheReason = `revalidate: ${finalRevalidate}`\n        }\n\n        if (\n          // when force static is configured we don't bail from\n          // `revalidate: 0` values\n          !(workStore.forceStatic && finalRevalidate === 0) &&\n          // we don't consider autoNoCache to switch to dynamic for ISR\n          !autoNoCache &&\n          // If the revalidate value isn't currently set or the value is less\n          // than the current revalidate value, we should update the revalidate\n          // value.\n          revalidateStore &&\n          finalRevalidate < revalidateStore.revalidate\n        ) {\n          // If we were setting the revalidate value to 0, we should try to\n          // postpone instead first.\n          if (finalRevalidate === 0) {\n            if (workUnitStore && workUnitStore.type === 'prerender') {\n              if (cacheSignal) {\n                cacheSignal.endRead()\n                cacheSignal = null\n              }\n              return makeHangingPromise<Response>(\n                workUnitStore.renderSignal,\n                'fetch()'\n              )\n            } else {\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `revalidate: 0 fetch ${input} ${workStore.route}`\n              )\n            }\n          }\n\n          // We only want to set the revalidate store's revalidate time if it\n          // was explicitly set for the fetch call, i.e. currentFetchRevalidate.\n          if (revalidateStore && currentFetchRevalidate === finalRevalidate) {\n            revalidateStore.revalidate = finalRevalidate\n          }\n        }\n\n        const isCacheableRevalidate =\n          typeof finalRevalidate === 'number' && finalRevalidate > 0\n\n        let cacheKey: string | undefined\n        const { incrementalCache } = workStore\n\n        const useCacheOrRequestStore =\n          workUnitStore?.type === 'request' || workUnitStore?.type === 'cache'\n            ? workUnitStore\n            : undefined\n\n        if (\n          incrementalCache &&\n          (isCacheableRevalidate ||\n            useCacheOrRequestStore?.serverComponentsHmrCache)\n        ) {\n          try {\n            cacheKey = await incrementalCache.generateCacheKey(\n              fetchUrl,\n              isRequestInput ? (input as RequestInit) : init\n            )\n          } catch (err) {\n            console.error(`Failed to generate cache key for`, input)\n          }\n        }\n\n        const fetchIdx = workStore.nextFetchId ?? 1\n        workStore.nextFetchId = fetchIdx + 1\n\n        let handleUnlock = () => Promise.resolve()\n\n        const doOriginalFetch = async (\n          isStale?: boolean,\n          cacheReasonOverride?: string\n        ) => {\n          const requestInputFields = [\n            'cache',\n            'credentials',\n            'headers',\n            'integrity',\n            'keepalive',\n            'method',\n            'mode',\n            'redirect',\n            'referrer',\n            'referrerPolicy',\n            'window',\n            'duplex',\n\n            // don't pass through signal when revalidating\n            ...(isStale ? [] : ['signal']),\n          ]\n\n          if (isRequestInput) {\n            const reqInput: Request = input as any\n            const reqOptions: RequestInit = {\n              body: (reqInput as any)._ogBody || reqInput.body,\n            }\n\n            for (const field of requestInputFields) {\n              // @ts-expect-error custom fields\n              reqOptions[field] = reqInput[field]\n            }\n            input = new Request(reqInput.url, reqOptions)\n          } else if (init) {\n            const { _ogBody, body, signal, ...otherInput } =\n              init as RequestInit & { _ogBody?: any }\n            init = {\n              ...otherInput,\n              body: _ogBody || body,\n              signal: isStale ? undefined : signal,\n            }\n          }\n\n          // add metadata to init without editing the original\n          const clonedInit = {\n            ...init,\n            next: { ...init?.next, fetchType: 'origin', fetchIdx },\n          }\n\n          return originFetch(input, clonedInit)\n            .then(async (res) => {\n              if (!isStale && fetchStart) {\n                trackFetchMetric(workStore, {\n                  start: fetchStart,\n                  url: fetchUrl,\n                  cacheReason: cacheReasonOverride || cacheReason,\n                  cacheStatus:\n                    finalRevalidate === 0 || cacheReasonOverride\n                      ? 'skip'\n                      : 'miss',\n                  cacheWarning,\n                  status: res.status,\n                  method: clonedInit.method || 'GET',\n                })\n              }\n              if (\n                res.status === 200 &&\n                incrementalCache &&\n                cacheKey &&\n                (isCacheableRevalidate ||\n                  useCacheOrRequestStore?.serverComponentsHmrCache)\n              ) {\n                const normalizedRevalidate =\n                  finalRevalidate >= INFINITE_CACHE\n                    ? CACHE_ONE_YEAR\n                    : finalRevalidate\n\n                if (workUnitStore && workUnitStore.type === 'prerender') {\n                  // We are prerendering at build time or revalidate time with dynamicIO so we need to\n                  // buffer the response so we can guarantee it can be read in a microtask\n                  const bodyBuffer = await res.arrayBuffer()\n\n                  const fetchedData = {\n                    headers: Object.fromEntries(res.headers.entries()),\n                    body: Buffer.from(bodyBuffer).toString('base64'),\n                    status: res.status,\n                    url: res.url,\n                  }\n\n                  // We can skip checking the serverComponentsHmrCache because we aren't in\n                  // dev mode.\n\n                  await incrementalCache.set(\n                    cacheKey,\n                    {\n                      kind: CachedRouteKind.FETCH,\n                      data: fetchedData,\n                      revalidate: normalizedRevalidate,\n                    },\n                    { fetchCache: true, fetchUrl, fetchIdx, tags }\n                  )\n                  await handleUnlock()\n\n                  // We return a new Response to the caller.\n                  return new Response(bodyBuffer, {\n                    headers: res.headers,\n                    status: res.status,\n                    statusText: res.statusText,\n                  })\n                } else {\n                  // We're cloning the response using this utility because there\n                  // exists a bug in the undici library around response cloning.\n                  // See the following pull request for more details:\n                  // https://github.com/vercel/next.js/pull/73274\n\n                  const [cloned1, cloned2] = cloneResponse(res)\n\n                  // We are dynamically rendering including dev mode. We want to return\n                  // the response to the caller as soon as possible because it might stream\n                  // over a very long time.\n                  cloned1\n                    .arrayBuffer()\n                    .then(async (arrayBuffer) => {\n                      const bodyBuffer = Buffer.from(arrayBuffer)\n\n                      const fetchedData = {\n                        headers: Object.fromEntries(cloned1.headers.entries()),\n                        body: bodyBuffer.toString('base64'),\n                        status: cloned1.status,\n                        url: cloned1.url,\n                      }\n\n                      useCacheOrRequestStore?.serverComponentsHmrCache?.set(\n                        cacheKey,\n                        fetchedData\n                      )\n\n                      if (isCacheableRevalidate) {\n                        await incrementalCache.set(\n                          cacheKey,\n                          {\n                            kind: CachedRouteKind.FETCH,\n                            data: fetchedData,\n                            revalidate: normalizedRevalidate,\n                          },\n                          { fetchCache: true, fetchUrl, fetchIdx, tags }\n                        )\n                      }\n                    })\n                    .catch((error) =>\n                      console.warn(`Failed to set fetch cache`, input, error)\n                    )\n                    .finally(handleUnlock)\n\n                  return cloned2\n                }\n              }\n\n              // we had response that we determined shouldn't be cached so we return it\n              // and don't cache it. This also needs to unlock the cache lock we acquired.\n              await handleUnlock()\n\n              return res\n            })\n            .catch((error) => {\n              handleUnlock()\n              throw error\n            })\n        }\n\n        let cacheReasonOverride\n        let isForegroundRevalidate = false\n        let isHmrRefreshCache = false\n\n        if (cacheKey && incrementalCache) {\n          let cachedFetchData: CachedFetchData | undefined\n\n          if (\n            useCacheOrRequestStore?.isHmrRefresh &&\n            useCacheOrRequestStore.serverComponentsHmrCache\n          ) {\n            cachedFetchData =\n              useCacheOrRequestStore.serverComponentsHmrCache.get(cacheKey)\n\n            isHmrRefreshCache = true\n          }\n\n          if (isCacheableRevalidate && !cachedFetchData) {\n            handleUnlock = await incrementalCache.lock(cacheKey)\n            const entry = workStore.isOnDemandRevalidate\n              ? null\n              : await incrementalCache.get(cacheKey, {\n                  kind: IncrementalCacheKind.FETCH,\n                  revalidate: finalRevalidate,\n                  fetchUrl,\n                  fetchIdx,\n                  tags,\n                  softTags: implicitTags?.tags,\n                })\n\n            if (hasNoExplicitCacheConfig) {\n              // We sometimes use the cache to dedupe fetches that do not specify a cache configuration\n              // In these cases we want to make sure we still exclude them from prerenders if dynamicIO is on\n              // so we introduce an artificial Task boundary here.\n              if (workUnitStore && workUnitStore.type === 'prerender') {\n                await waitAtLeastOneReactRenderTask()\n              }\n            }\n\n            if (entry) {\n              await handleUnlock()\n            } else {\n              // in dev, incremental cache response will be null in case the browser adds `cache-control: no-cache` in the request headers\n              cacheReasonOverride = 'cache-control: no-cache (hard refresh)'\n            }\n\n            if (entry?.value && entry.value.kind === CachedRouteKind.FETCH) {\n              // when stale and is revalidating we wait for fresh data\n              // so the revalidated entry has the updated data\n              if (workStore.isRevalidate && entry.isStale) {\n                isForegroundRevalidate = true\n              } else {\n                if (entry.isStale) {\n                  workStore.pendingRevalidates ??= {}\n                  if (!workStore.pendingRevalidates[cacheKey]) {\n                    const pendingRevalidate = doOriginalFetch(true)\n                      .then(async (response) => ({\n                        body: await response.arrayBuffer(),\n                        headers: response.headers,\n                        status: response.status,\n                        statusText: response.statusText,\n                      }))\n                      .finally(() => {\n                        workStore.pendingRevalidates ??= {}\n                        delete workStore.pendingRevalidates[cacheKey || '']\n                      })\n\n                    // Attach the empty catch here so we don't get a \"unhandled\n                    // promise rejection\" warning.\n                    pendingRevalidate.catch(console.error)\n\n                    workStore.pendingRevalidates[cacheKey] = pendingRevalidate\n                  }\n                }\n\n                cachedFetchData = entry.value.data\n              }\n            }\n          }\n\n          if (cachedFetchData) {\n            if (fetchStart) {\n              trackFetchMetric(workStore, {\n                start: fetchStart,\n                url: fetchUrl,\n                cacheReason,\n                cacheStatus: isHmrRefreshCache ? 'hmr' : 'hit',\n                cacheWarning,\n                status: cachedFetchData.status || 200,\n                method: init?.method || 'GET',\n              })\n            }\n\n            const response = new Response(\n              Buffer.from(cachedFetchData.body, 'base64'),\n              {\n                headers: cachedFetchData.headers,\n                status: cachedFetchData.status,\n              }\n            )\n\n            Object.defineProperty(response, 'url', {\n              value: cachedFetchData.url,\n            })\n\n            return response\n          }\n        }\n\n        if (workStore.isStaticGeneration && init && typeof init === 'object') {\n          const { cache } = init\n\n          // Delete `cache` property as Cloudflare Workers will throw an error\n          if (isEdgeRuntime) delete init.cache\n\n          if (cache === 'no-store') {\n            // If enabled, we should bail out of static generation.\n            if (workUnitStore && workUnitStore.type === 'prerender') {\n              if (cacheSignal) {\n                cacheSignal.endRead()\n                cacheSignal = null\n              }\n              return makeHangingPromise<Response>(\n                workUnitStore.renderSignal,\n                'fetch()'\n              )\n            } else {\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `no-store fetch ${input} ${workStore.route}`\n              )\n            }\n          }\n\n          const hasNextConfig = 'next' in init\n          const { next = {} } = init\n          if (\n            typeof next.revalidate === 'number' &&\n            revalidateStore &&\n            next.revalidate < revalidateStore.revalidate\n          ) {\n            if (next.revalidate === 0) {\n              // If enabled, we should bail out of static generation.\n              if (workUnitStore && workUnitStore.type === 'prerender') {\n                return makeHangingPromise<Response>(\n                  workUnitStore.renderSignal,\n                  'fetch()'\n                )\n              } else {\n                markCurrentScopeAsDynamic(\n                  workStore,\n                  workUnitStore,\n                  `revalidate: 0 fetch ${input} ${workStore.route}`\n                )\n              }\n            }\n\n            if (!workStore.forceStatic || next.revalidate !== 0) {\n              revalidateStore.revalidate = next.revalidate\n            }\n          }\n          if (hasNextConfig) delete init.next\n        }\n\n        // if we are revalidating the whole page via time or on-demand and\n        // the fetch cache entry is stale we should still de-dupe the\n        // origin hit if it's a cache-able entry\n        if (cacheKey && isForegroundRevalidate) {\n          const pendingRevalidateKey = cacheKey\n          workStore.pendingRevalidates ??= {}\n          let pendingRevalidate =\n            workStore.pendingRevalidates[pendingRevalidateKey]\n\n          if (pendingRevalidate) {\n            const revalidatedResult: {\n              body: ArrayBuffer\n              headers: Headers\n              status: number\n              statusText: string\n            } = await pendingRevalidate\n            return new Response(revalidatedResult.body, {\n              headers: revalidatedResult.headers,\n              status: revalidatedResult.status,\n              statusText: revalidatedResult.statusText,\n            })\n          }\n\n          // We used to just resolve the Response and clone it however for\n          // static generation with dynamicIO we need the response to be able to\n          // be resolved in a microtask and cloning the response will never have\n          // a body that can resolve in a microtask in node (as observed through\n          // experimentation) So instead we await the body and then when it is\n          // available we construct manually cloned Response objects with the\n          // body as an ArrayBuffer. This will be resolvable in a microtask\n          // making it compatible with dynamicIO.\n          const pendingResponse = doOriginalFetch(true, cacheReasonOverride)\n            // We're cloning the response using this utility because there\n            // exists a bug in the undici library around response cloning.\n            // See the following pull request for more details:\n            // https://github.com/vercel/next.js/pull/73274\n            .then(cloneResponse)\n\n          pendingRevalidate = pendingResponse\n            .then(async (responses) => {\n              const response = responses[0]\n              return {\n                body: await response.arrayBuffer(),\n                headers: response.headers,\n                status: response.status,\n                statusText: response.statusText,\n              }\n            })\n            .finally(() => {\n              // If the pending revalidate is not present in the store, then\n              // we have nothing to delete.\n              if (!workStore.pendingRevalidates?.[pendingRevalidateKey]) {\n                return\n              }\n\n              delete workStore.pendingRevalidates[pendingRevalidateKey]\n            })\n\n          // Attach the empty catch here so we don't get a \"unhandled promise\n          // rejection\" warning\n          pendingRevalidate.catch(() => {})\n\n          workStore.pendingRevalidates[pendingRevalidateKey] = pendingRevalidate\n\n          return pendingResponse.then((responses) => responses[1])\n        } else {\n          return doOriginalFetch(false, cacheReasonOverride)\n        }\n      }\n    )\n\n    if (cacheSignal) {\n      try {\n        return await result\n      } finally {\n        if (cacheSignal) {\n          cacheSignal.endRead()\n        }\n      }\n    }\n    return result\n  }\n\n  // Attach the necessary properties to the patched fetch function.\n  // We don't use this to determine if the fetch function has been patched,\n  // but for external consumers to determine if the fetch function has been\n  // patched.\n  patched.__nextPatched = true as const\n  patched.__nextGetStaticStore = () => workAsyncStorage\n  patched._nextOriginalFetch = originFetch\n  ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = true\n\n  return patched\n}\n// we patch fetch to collect cache information used for\n// determining if a page is static or not\nexport function patchFetch(options: PatchableModule) {\n  // If we've already patched fetch, we should not patch it again.\n  if (isFetchPatched()) return\n\n  // Grab the original fetch function. We'll attach this so we can use it in\n  // the patched fetch function.\n  const original = createDedupeFetch(globalThis.fetch)\n\n  // Set the global fetch to the patched fetch.\n  globalThis.fetch = createPatchedFetcher(original, options)\n}\n"], "names": ["AppRenderSpan", "NextNodeServerSpan", "getTracer", "SpanKind", "CACHE_ONE_YEAR", "INFINITE_CACHE", "NEXT_CACHE_TAG_MAX_ITEMS", "NEXT_CACHE_TAG_MAX_LENGTH", "markCurrentScopeAsDynamic", "makeHangingPromise", "createDedupeFetch", "CachedRouteKind", "IncrementalCacheKind", "waitAtLeastOneReactRenderTask", "cloneResponse", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "NEXT_PATCH_SYMBOL", "Symbol", "for", "isFetchPatched", "globalThis", "validateRevalidate", "revalidateVal", "route", "normalizedRevalidate", "undefined", "isNaN", "Error", "err", "message", "includes", "validateTags", "tags", "description", "validTags", "invalidTags", "i", "length", "tag", "push", "reason", "console", "warn", "slice", "join", "log", "trackFetchMetric", "workStore", "ctx", "requestEndedState", "ended", "isDebugBuild", "NEXT_DEBUG_BUILD", "NEXT_SSG_FETCH_METRICS", "isStaticGeneration", "isDevelopment", "NODE_ENV", "fetchMetrics", "end", "performance", "<PERSON><PERSON><PERSON><PERSON>", "now", "idx", "nextFetchId", "createPatchedFetcher", "originFetch", "workAsyncStorage", "workUnitAsyncStorage", "patched", "input", "init", "url", "URL", "Request", "username", "password", "fetchUrl", "href", "method", "toUpperCase", "isInternal", "next", "internal", "hideSpan", "NEXT_OTEL_FETCH_DISABLED", "fetchStart", "getStore", "workUnitStore", "cacheSignal", "type", "beginRead", "result", "trace", "internalFetch", "fetch", "kind", "CLIENT", "spanName", "filter", "Boolean", "attributes", "hostname", "port", "getRequestMeta", "isDraftMode", "isRequestInput", "field", "value", "finalRevalidate", "getNextField", "currentFetchRevalidate", "toString", "revalidateStore", "Array", "isArray", "collectedTags", "implicitTags", "pageFetchCacheMode", "fetchCache", "isUsingNoStore", "isUnstableNoStore", "currentFetchCacheConfig", "cacheReason", "cacheWarning", "isConflictingRevalidate", "hasExplicitFetchCacheOptOut", "noFetchConfigAndForceDynamic", "forceDynamic", "_headers", "initHeaders", "get", "Headers", "hasUnCacheableHeader", "isUnCacheableMethod", "toLowerCase", "hasNoExplicitCacheConfig", "autoNoCache", "isPrerendering", "revalidate", "endRead", "renderSignal", "forceStatic", "isCacheableRevalidate", "cache<PERSON>ey", "incrementalCache", "useCacheOrRequestStore", "serverComponentsHmrCache", "generate<PERSON>ache<PERSON>ey", "error", "fetchIdx", "handleUnlock", "Promise", "resolve", "doOriginalFetch", "isStale", "cacheReasonOverride", "requestInputFields", "reqInput", "reqOptions", "body", "_ogBody", "signal", "otherInput", "clonedInit", "fetchType", "then", "res", "start", "cacheStatus", "status", "bodyBuffer", "arrayBuffer", "fetchedData", "headers", "Object", "fromEntries", "entries", "<PERSON><PERSON><PERSON>", "from", "set", "FETCH", "data", "Response", "statusText", "cloned1", "cloned2", "catch", "finally", "isForegroundRevalidate", "isHmrRefreshCache", "cachedFetchData", "isHmrRefresh", "lock", "entry", "isOnDemandRevalidate", "softTags", "isRevalidate", "pendingRevalidates", "pendingRevalidate", "response", "defineProperty", "cache", "hasNextConfig", "pendingRevalidateKey", "revalidatedResult", "pendingResponse", "responses", "__nextPatched", "__nextGetStaticStore", "_nextOriginalFetch", "patchFetch", "options", "original"], "mappings": "AAKA,SAASA,aAAa,EAAEC,kBAAkB,QAAQ,oBAAmB;AACrE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,iBAAgB;AACpD,SACEC,cAAc,EACdC,cAAc,EACdC,wBAAwB,EACxBC,yBAAyB,QACpB,sBAAqB;AAC5B,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,kBAAkB,QAAQ,6BAA4B;AAE/D,SAASC,iBAAiB,QAAQ,iBAAgB;AAElD,SACEC,eAAe,EACfC,oBAAoB,QAEf,oBAAmB;AAC1B,SAASC,6BAA6B,QAAQ,sBAAqB;AACnE,SAASC,aAAa,QAAQ,mBAAkB;AAEhD,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAUnD,OAAO,MAAMC,oBAAoBC,OAAOC,GAAG,CAAC,cAAa;AAEzD,SAASC;IACP,OAAO,AAACC,UAAsC,CAACJ,kBAAkB,KAAK;AACxE;AAEA,OAAO,SAASK,mBACdC,aAAsB,EACtBC,KAAa;IAEb,IAAI;QACF,IAAIC,uBAA2CC;QAE/C,IAAIH,kBAAkB,OAAO;YAC3BE,uBAAuBtB;QACzB,OAAO,IACL,OAAOoB,kBAAkB,YACzB,CAACI,MAAMJ,kBACPA,gBAAgB,CAAC,GACjB;YACAE,uBAAuBF;QACzB,OAAO,IAAI,OAAOA,kBAAkB,aAAa;YAC/C,MAAM,qBAEL,CAFK,IAAIK,MACR,CAAC,0BAA0B,EAAEL,cAAc,MAAM,EAAEC,MAAM,yCAAyC,CAAC,GAD/F,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAOC;IACT,EAAE,OAAOI,KAAU;QACjB,0EAA0E;QAC1E,IAAIA,eAAeD,SAASC,IAAIC,OAAO,CAACC,QAAQ,CAAC,uBAAuB;YACtE,MAAMF;QACR;QACA,OAAOH;IACT;AACF;AAEA,OAAO,SAASM,aAAaC,IAAW,EAAEC,WAAmB;IAC3D,MAAMC,YAAsB,EAAE;IAC9B,MAAMC,cAGD,EAAE;IAEP,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,KAAKK,MAAM,EAAED,IAAK;QACpC,MAAME,MAAMN,IAAI,CAACI,EAAE;QAEnB,IAAI,OAAOE,QAAQ,UAAU;YAC3BH,YAAYI,IAAI,CAAC;gBAAED;gBAAKE,QAAQ;YAAiC;QACnE,OAAO,IAAIF,IAAID,MAAM,GAAGjC,2BAA2B;YACjD+B,YAAYI,IAAI,CAAC;gBACfD;gBACAE,QAAQ,CAAC,uBAAuB,EAAEpC,2BAA2B;YAC/D;QACF,OAAO;YACL8B,UAAUK,IAAI,CAACD;QACjB;QAEA,IAAIJ,UAAUG,MAAM,GAAGlC,0BAA0B;YAC/CsC,QAAQC,IAAI,CACV,CAAC,oCAAoC,EAAET,YAAY,eAAe,CAAC,EACnED,KAAKW,KAAK,CAACP,GAAGQ,IAAI,CAAC;YAErB;QACF;IACF;IAEA,IAAIT,YAAYE,MAAM,GAAG,GAAG;QAC1BI,QAAQC,IAAI,CAAC,CAAC,gCAAgC,EAAET,YAAY,EAAE,CAAC;QAE/D,KAAK,MAAM,EAAEK,GAAG,EAAEE,MAAM,EAAE,IAAIL,YAAa;YACzCM,QAAQI,GAAG,CAAC,CAAC,MAAM,EAAEP,IAAI,EAAE,EAAEE,QAAQ;QACvC;IACF;IACA,OAAON;AACT;AAEA,SAASY,iBACPC,SAAoB,EACpBC,GAAqC;QAIjCD;IAFJ,4EAA4E;IAC5E,IAAI,CAACA,WAAW;IAChB,KAAIA,+BAAAA,UAAUE,iBAAiB,qBAA3BF,6BAA6BG,KAAK,EAAE;IAExC,MAAMC,eACJ,AAAC,CAAA,CAAC,CAACtC,QAAQC,GAAG,CAACsC,gBAAgB,IAC7BvC,QAAQC,GAAG,CAACuC,sBAAsB,KAAK,GAAE,KAC3CN,UAAUO,kBAAkB;IAC9B,MAAMC,gBAAgB1C,QAAQC,GAAG,CAAC0C,QAAQ,KAAK;IAE/C,IACE,8EAA8E;IAC9E,gEAAgE;IAChE,CAACL,gBACD,CAACI,eACD;QACA;IACF;IAEAR,UAAUU,YAAY,KAAK,EAAE;IAE7BV,UAAUU,YAAY,CAAClB,IAAI,CAAC;QAC1B,GAAGS,GAAG;QACNU,KAAKC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAC7CC,KAAKf,UAAUgB,WAAW,IAAI;IAChC;AACF;AAOA,OAAO,SAASC,qBACdC,WAAoB,EACpB,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAmB;IAE3D,yEAAyE;IACzE,iDAAiD;IACjD,MAAMC,UAAU,OACdC,OACAC;YAYeA,cAIKA;QAdpB,IAAIC;QACJ,IAAI;YACFA,MAAM,IAAIC,IAAIH,iBAAiBI,UAAUJ,MAAME,GAAG,GAAGF;YACrDE,IAAIG,QAAQ,GAAG;YACfH,IAAII,QAAQ,GAAG;QACjB,EAAE,OAAM;YACN,kEAAkE;YAClEJ,MAAM9C;QACR;QACA,MAAMmD,WAAWL,CAAAA,uBAAAA,IAAKM,IAAI,KAAI;QAC9B,MAAMC,SAASR,CAAAA,yBAAAA,eAAAA,KAAMQ,MAAM,qBAAZR,aAAcS,WAAW,OAAM;QAE9C,yDAAyD;QACzD,oBAAoB;QACpB,MAAMC,aAAa,CAACV,yBAAAA,aAAAA,KAAMW,IAAI,qBAAX,AAACX,WAAoBY,QAAQ,MAAK;QACrD,MAAMC,WAAWtE,QAAQC,GAAG,CAACsE,wBAAwB,KAAK;QAC1D,oDAAoD;QACpD,2EAA2E;QAC3E,mEAAmE;QACnE,6DAA6D;QAC7D,MAAMC,aAAiCL,aACnCvD,YACAkC,YAAYC,UAAU,GAAGD,YAAYE,GAAG;QAE5C,MAAMd,YAAYmB,iBAAiBoB,QAAQ;QAC3C,MAAMC,gBAAgBpB,qBAAqBmB,QAAQ;QAEnD,sFAAsF;QACtF,IAAIE,cACFD,iBAAiBA,cAAcE,IAAI,KAAK,cACpCF,cAAcC,WAAW,GACzB;QACN,IAAIA,aAAa;YACfA,YAAYE,SAAS;QACvB;QAEA,MAAMC,SAAS5F,YAAY6F,KAAK,CAC9BZ,aAAalF,mBAAmB+F,aAAa,GAAGhG,cAAciG,KAAK,EACnE;YACEX;YACAY,MAAM/F,SAASgG,MAAM;YACrBC,UAAU;gBAAC;gBAASnB;gBAAQF;aAAS,CAACsB,MAAM,CAACC,SAASvD,IAAI,CAAC;YAC3DwD,YAAY;gBACV,YAAYxB;gBACZ,eAAeE;gBACf,eAAe,EAAEP,uBAAAA,IAAK8B,QAAQ;gBAC9B,iBAAiB9B,CAAAA,uBAAAA,IAAK+B,IAAI,KAAI7E;YAChC;QACF,GACA;gBAkKI8E;YAjKF,wEAAwE;YACxE,IAAIvB,YAAY;gBACd,OAAOf,YAAYI,OAAOC;YAC5B;YAEA,qDAAqD;YACrD,iEAAiE;YACjE,wBAAwB;YACxB,IAAI,CAACvB,WAAW;gBACd,OAAOkB,YAAYI,OAAOC;YAC5B;YAEA,qEAAqE;YACrE,iEAAiE;YACjE,IAAIvB,UAAUyD,WAAW,EAAE;gBACzB,OAAOvC,YAAYI,OAAOC;YAC5B;YAEA,MAAMmC,iBACJpC,SACA,OAAOA,UAAU,YACjB,OAAO,AAACA,MAAkBS,MAAM,KAAK;YAEvC,MAAMyB,iBAAiB,CAACG;gBACtB,0EAA0E;gBAC1E,MAAMC,QAASrC,wBAAD,AAACA,IAAc,CAACoC,MAAM;gBACpC,OAAOC,SAAUF,CAAAA,iBAAiB,AAACpC,KAAa,CAACqC,MAAM,GAAG,IAAG;YAC/D;YAEA,IAAIE,kBAAsCnF;YAC1C,MAAMoF,eAAe,CAACH;oBACNpC,YACVA,aAEE;gBAHN,OAAO,QAAOA,yBAAAA,aAAAA,KAAMW,IAAI,qBAAVX,UAAY,CAACoC,MAAM,MAAK,cAClCpC,yBAAAA,cAAAA,KAAMW,IAAI,qBAAVX,WAAY,CAACoC,MAAM,GACnBD,kBACE,cAAA,AAACpC,MAAcY,IAAI,qBAAnB,WAAqB,CAACyB,MAAM,GAC5BjF;YACR;YACA,0DAA0D;YAC1D,0CAA0C;YAC1C,IAAIqF,yBAAyBD,aAAa;YAC1C,MAAM7E,OAAiBD,aACrB8E,aAAa,WAAW,EAAE,EAC1B,CAAC,MAAM,EAAExC,MAAM0C,QAAQ,IAAI;YAG7B,MAAMC,kBACJzB,iBACCA,CAAAA,cAAcE,IAAI,KAAK,WACtBF,cAAcE,IAAI,KAAK,eACvBF,cAAcE,IAAI,KAAK,mBACvBF,cAAcE,IAAI,KAAK,kBAAiB,IACtCF,gBACA9D;YAEN,IAAIuF,iBAAiB;gBACnB,IAAIC,MAAMC,OAAO,CAAClF,OAAO;oBACvB,wDAAwD;oBACxD,MAAMmF,gBACJH,gBAAgBhF,IAAI,IAAKgF,CAAAA,gBAAgBhF,IAAI,GAAG,EAAE,AAAD;oBACnD,KAAK,MAAMM,OAAON,KAAM;wBACtB,IAAI,CAACmF,cAAcrF,QAAQ,CAACQ,MAAM;4BAChC6E,cAAc5E,IAAI,CAACD;wBACrB;oBACF;gBACF;YACF;YAEA,MAAM8E,eAAe7B,iCAAAA,cAAe6B,YAAY;YAEhD,sEAAsE;YACtE,QAAQ;YACR,MAAMC,qBACJ9B,iBAAiBA,cAAcE,IAAI,KAAK,mBACpC,mBACA1C,UAAUuE,UAAU;YAE1B,MAAMC,iBAAiB,CAAC,CAACxE,UAAUyE,iBAAiB;YAEpD,IAAIC,0BAA0BlB,eAAe;YAC7C,IAAImB,cAAc;YAClB,IAAIC;YAEJ,IACE,OAAOF,4BAA4B,YACnC,OAAOX,2BAA2B,aAClC;gBACA,oHAAoH;gBACpH,MAAMc,0BAEJ,AADA,uCAAuC;gBACtCH,4BAA4B,iBAC3BX,2BAA2B,KAC7B,0DAA0D;gBACzDW,4BAA4B,cAC1BX,CAAAA,yBAAyB,KAAKA,2BAA2B,KAAI;gBAElE,IAAIc,yBAAyB;oBAC3BD,eAAe,CAAC,kBAAkB,EAAEF,wBAAwB,mBAAmB,EAAEX,uBAAuB,gCAAgC,CAAC;oBACzIW,0BAA0BhG;oBAC1BqF,yBAAyBrF;gBAC3B;YACF;YAEA,MAAMoG,8BACJ,2CAA2C;YAC3CJ,4BAA4B,cAC5BA,4BAA4B,cAC5B,6FAA6F;YAC7F,gFAAgF;YAChFJ,uBAAuB,oBACvBA,uBAAuB;YAEzB,gFAAgF;YAChF,+EAA+E;YAC/E,sFAAsF;YACtF,wFAAwF;YACxF,wBAAwB;YACxB,MAAMS,+BACJ,CAACT,sBACD,CAACI,2BACD,CAACX,0BACD/D,UAAUgF,YAAY;YAExB,IACE,6FAA6F;YAC7F,gDAAgD;YAChDN,4BAA4B,iBAC5B,OAAOX,2BAA2B,aAClC;gBACAA,yBAAyB;YAC3B,OAAO,IACL,mDAAmD;YACnD,0DAA0D;YAC1D,uBAAuB;YACvBvB,CAAAA,iCAAAA,cAAeE,IAAI,MAAK,WACvBoC,CAAAA,+BAA+BC,4BAA2B,GAC3D;gBACAhB,yBAAyB;YAC3B;YAEA,IACEW,4BAA4B,cAC5BA,4BAA4B,YAC5B;gBACAC,cAAc,CAAC,OAAO,EAAED,yBAAyB;YACnD;YAEAb,kBAAkBvF,mBAChByF,wBACA/D,UAAUxB,KAAK;YAGjB,MAAMyG,WAAWzB,eAAe;YAChC,MAAM0B,cACJ,QAAOD,4BAAAA,SAAUE,GAAG,MAAK,aACrBF,WACA,IAAIG,QAAQH,YAAY,CAAC;YAE/B,MAAMI,uBACJH,YAAYC,GAAG,CAAC,oBAAoBD,YAAYC,GAAG,CAAC;YAEtD,MAAMG,sBAAsB,CAAC;gBAAC;gBAAO;aAAO,CAACvG,QAAQ,CACnDyE,EAAAA,kBAAAA,eAAe,8BAAfA,gBAA0B+B,WAAW,OAAM;YAG7C;;;;;;;;;SASC,GACD,MAAMC,2BACJ,kCAAkC;YAClClB,sBAAsB5F,aACtB,kCAAkC;YACjCgG,CAAAA,2BAA2BhG,aAC1B,+EAA+E;YAC/E,yEAAyE;YACzEgG,4BAA4B,SAAQ,KACtC,kCAAkC;YAClCX,0BAA0BrF;YAC5B,MAAM+G,cAGJ,AAFA,2CAA2C;YAC3C,kCAAkC;YACjCD,4BACC,sFAAsF;YACtF,+CAA+C;YAC/C,CAACxF,UAAU0F,cAAc,IAC1B,AAACL,CAAAA,wBAAwBC,mBAAkB,KAC1CrB,mBACAA,gBAAgB0B,UAAU,KAAK;YAEnC,IACEH,4BACAhD,kBAAkB9D,aAClB8D,cAAcE,IAAI,KAAK,aACvB;gBACA,8FAA8F;gBAC9F,4CAA4C;gBAC5C,IAAID,aAAa;oBACfA,YAAYmD,OAAO;oBACnBnD,cAAc;gBAChB;gBACA,OAAOlF,mBACLiF,cAAcqD,YAAY,EAC1B;YAEJ;YAEA,OAAQvB;gBACN,KAAK;oBAAkB;wBACrBK,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAiB;wBACpB,IACED,4BAA4B,iBAC3B,OAAOb,oBAAoB,eAAeA,kBAAkB,GAC7D;4BACA,MAAM,qBAEL,CAFK,IAAIjF,MACR,CAAC,uCAAuC,EAAEiD,SAAS,gDAAgD,CAAC,GADhG,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACA8C,cAAc;wBACd;oBACF;gBACA,KAAK;oBAAc;wBACjB,IAAID,4BAA4B,YAAY;4BAC1C,MAAM,qBAEL,CAFK,IAAI9F,MACR,CAAC,oCAAoC,EAAEiD,SAAS,6CAA6C,CAAC,GAD1F,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACA;oBACF;gBACA,KAAK;oBAAe;wBAClB,IACE,OAAOkC,2BAA2B,eAClCA,2BAA2B,GAC3B;4BACAY,cAAc;4BACdd,kBAAkB1G;wBACpB;wBACA;oBACF;gBACA;YAKF;YAEA,IAAI,OAAO0G,oBAAoB,aAAa;gBAC1C,IAAIS,uBAAuB,mBAAmB,CAACE,gBAAgB;oBAC7DX,kBAAkB1G;oBAClBwH,cAAc;gBAChB,OAAO,IAAIL,uBAAuB,oBAAoB;oBACpDT,kBAAkB;oBAClBc,cAAc;gBAChB,OAAO,IAAIH,gBAAgB;oBACzBX,kBAAkB;oBAClBc,cAAc;gBAChB,OAAO,IAAIc,aAAa;oBACtB5B,kBAAkB;oBAClBc,cAAc;gBAChB,OAAO;oBACL,mDAAmD;oBACnDA,cAAc;oBACdd,kBAAkBI,kBACdA,gBAAgB0B,UAAU,GAC1BxI;gBACN;YACF,OAAO,IAAI,CAACwH,aAAa;gBACvBA,cAAc,CAAC,YAAY,EAAEd,iBAAiB;YAChD;YAEA,IACE,qDAAqD;YACrD,yBAAyB;YACzB,CAAE7D,CAAAA,UAAU8F,WAAW,IAAIjC,oBAAoB,CAAA,KAC/C,6DAA6D;YAC7D,CAAC4B,eACD,mEAAmE;YACnE,qEAAqE;YACrE,SAAS;YACTxB,mBACAJ,kBAAkBI,gBAAgB0B,UAAU,EAC5C;gBACA,iEAAiE;gBACjE,0BAA0B;gBAC1B,IAAI9B,oBAAoB,GAAG;oBACzB,IAAIrB,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;wBACvD,IAAID,aAAa;4BACfA,YAAYmD,OAAO;4BACnBnD,cAAc;wBAChB;wBACA,OAAOlF,mBACLiF,cAAcqD,YAAY,EAC1B;oBAEJ,OAAO;wBACLvI,0BACE0C,WACAwC,eACA,CAAC,oBAAoB,EAAElB,MAAM,CAAC,EAAEtB,UAAUxB,KAAK,EAAE;oBAErD;gBACF;gBAEA,mEAAmE;gBACnE,sEAAsE;gBACtE,IAAIyF,mBAAmBF,2BAA2BF,iBAAiB;oBACjEI,gBAAgB0B,UAAU,GAAG9B;gBAC/B;YACF;YAEA,MAAMkC,wBACJ,OAAOlC,oBAAoB,YAAYA,kBAAkB;YAE3D,IAAImC;YACJ,MAAM,EAAEC,gBAAgB,EAAE,GAAGjG;YAE7B,MAAMkG,yBACJ1D,CAAAA,iCAAAA,cAAeE,IAAI,MAAK,aAAaF,CAAAA,iCAAAA,cAAeE,IAAI,MAAK,UACzDF,gBACA9D;YAEN,IACEuH,oBACCF,CAAAA,0BACCG,0CAAAA,uBAAwBC,wBAAwB,CAAD,GACjD;gBACA,IAAI;oBACFH,WAAW,MAAMC,iBAAiBG,gBAAgB,CAChDvE,UACA6B,iBAAkBpC,QAAwBC;gBAE9C,EAAE,OAAO1C,KAAK;oBACZa,QAAQ2G,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE/E;gBACpD;YACF;YAEA,MAAMgF,WAAWtG,UAAUgB,WAAW,IAAI;YAC1ChB,UAAUgB,WAAW,GAAGsF,WAAW;YAEnC,IAAIC,eAAe,IAAMC,QAAQC,OAAO;YAExC,MAAMC,kBAAkB,OACtBC,SACAC;gBAEA,MAAMC,qBAAqB;oBACzB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBAEA,8CAA8C;uBAC1CF,UAAU,EAAE,GAAG;wBAAC;qBAAS;iBAC9B;gBAED,IAAIjD,gBAAgB;oBAClB,MAAMoD,WAAoBxF;oBAC1B,MAAMyF,aAA0B;wBAC9BC,MAAM,AAACF,SAAiBG,OAAO,IAAIH,SAASE,IAAI;oBAClD;oBAEA,KAAK,MAAMrD,SAASkD,mBAAoB;wBACtC,iCAAiC;wBACjCE,UAAU,CAACpD,MAAM,GAAGmD,QAAQ,CAACnD,MAAM;oBACrC;oBACArC,QAAQ,IAAII,QAAQoF,SAAStF,GAAG,EAAEuF;gBACpC,OAAO,IAAIxF,MAAM;oBACf,MAAM,EAAE0F,OAAO,EAAED,IAAI,EAAEE,MAAM,EAAE,GAAGC,YAAY,GAC5C5F;oBACFA,OAAO;wBACL,GAAG4F,UAAU;wBACbH,MAAMC,WAAWD;wBACjBE,QAAQP,UAAUjI,YAAYwI;oBAChC;gBACF;gBAEA,oDAAoD;gBACpD,MAAME,aAAa;oBACjB,GAAG7F,IAAI;oBACPW,MAAM;2BAAKX,wBAAAA,KAAMW,IAAI,AAAb;wBAAemF,WAAW;wBAAUf;oBAAS;gBACvD;gBAEA,OAAOpF,YAAYI,OAAO8F,YACvBE,IAAI,CAAC,OAAOC;oBACX,IAAI,CAACZ,WAAWrE,YAAY;wBAC1BvC,iBAAiBC,WAAW;4BAC1BwH,OAAOlF;4BACPd,KAAKK;4BACL8C,aAAaiC,uBAAuBjC;4BACpC8C,aACE5D,oBAAoB,KAAK+C,sBACrB,SACA;4BACNhC;4BACA8C,QAAQH,IAAIG,MAAM;4BAClB3F,QAAQqF,WAAWrF,MAAM,IAAI;wBAC/B;oBACF;oBACA,IACEwF,IAAIG,MAAM,KAAK,OACfzB,oBACAD,YACCD,CAAAA,0BACCG,0CAAAA,uBAAwBC,wBAAwB,CAAD,GACjD;wBACA,MAAM1H,uBACJoF,mBAAmB1G,iBACfD,iBACA2G;wBAEN,IAAIrB,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;4BACvD,oFAAoF;4BACpF,wEAAwE;4BACxE,MAAMiF,aAAa,MAAMJ,IAAIK,WAAW;4BAExC,MAAMC,cAAc;gCAClBC,SAASC,OAAOC,WAAW,CAACT,IAAIO,OAAO,CAACG,OAAO;gCAC/CjB,MAAMkB,OAAOC,IAAI,CAACR,YAAY3D,QAAQ,CAAC;gCACvC0D,QAAQH,IAAIG,MAAM;gCAClBlG,KAAK+F,IAAI/F,GAAG;4BACd;4BAEA,yEAAyE;4BACzE,YAAY;4BAEZ,MAAMyE,iBAAiBmC,GAAG,CACxBpC,UACA;gCACEhD,MAAMvF,gBAAgB4K,KAAK;gCAC3BC,MAAMT;gCACNlC,YAAYlH;4BACd,GACA;gCAAE8F,YAAY;gCAAM1C;gCAAUyE;gCAAUrH;4BAAK;4BAE/C,MAAMsH;4BAEN,0CAA0C;4BAC1C,OAAO,IAAIgC,SAASZ,YAAY;gCAC9BG,SAASP,IAAIO,OAAO;gCACpBJ,QAAQH,IAAIG,MAAM;gCAClBc,YAAYjB,IAAIiB,UAAU;4BAC5B;wBACF,OAAO;4BACL,8DAA8D;4BAC9D,8DAA8D;4BAC9D,mDAAmD;4BACnD,+CAA+C;4BAE/C,MAAM,CAACC,SAASC,QAAQ,GAAG9K,cAAc2J;4BAEzC,qEAAqE;4BACrE,yEAAyE;4BACzE,yBAAyB;4BACzBkB,QACGb,WAAW,GACXN,IAAI,CAAC,OAAOM;oCAUX1B;gCATA,MAAMyB,aAAaO,OAAOC,IAAI,CAACP;gCAE/B,MAAMC,cAAc;oCAClBC,SAASC,OAAOC,WAAW,CAACS,QAAQX,OAAO,CAACG,OAAO;oCACnDjB,MAAMW,WAAW3D,QAAQ,CAAC;oCAC1B0D,QAAQe,QAAQf,MAAM;oCACtBlG,KAAKiH,QAAQjH,GAAG;gCAClB;gCAEA0E,2CAAAA,mDAAAA,uBAAwBC,wBAAwB,qBAAhDD,iDAAkDkC,GAAG,CACnDpC,UACA6B;gCAGF,IAAI9B,uBAAuB;oCACzB,MAAME,iBAAiBmC,GAAG,CACxBpC,UACA;wCACEhD,MAAMvF,gBAAgB4K,KAAK;wCAC3BC,MAAMT;wCACNlC,YAAYlH;oCACd,GACA;wCAAE8F,YAAY;wCAAM1C;wCAAUyE;wCAAUrH;oCAAK;gCAEjD;4BACF,GACC0J,KAAK,CAAC,CAACtC,QACN3G,QAAQC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE2B,OAAO+E,QAElDuC,OAAO,CAACrC;4BAEX,OAAOmC;wBACT;oBACF;oBAEA,yEAAyE;oBACzE,4EAA4E;oBAC5E,MAAMnC;oBAEN,OAAOgB;gBACT,GACCoB,KAAK,CAAC,CAACtC;oBACNE;oBACA,MAAMF;gBACR;YACJ;YAEA,IAAIO;YACJ,IAAIiC,yBAAyB;YAC7B,IAAIC,oBAAoB;YAExB,IAAI9C,YAAYC,kBAAkB;gBAChC,IAAI8C;gBAEJ,IACE7C,CAAAA,0CAAAA,uBAAwB8C,YAAY,KACpC9C,uBAAuBC,wBAAwB,EAC/C;oBACA4C,kBACE7C,uBAAuBC,wBAAwB,CAAChB,GAAG,CAACa;oBAEtD8C,oBAAoB;gBACtB;gBAEA,IAAI/C,yBAAyB,CAACgD,iBAAiB;oBAC7CxC,eAAe,MAAMN,iBAAiBgD,IAAI,CAACjD;oBAC3C,MAAMkD,QAAQlJ,UAAUmJ,oBAAoB,GACxC,OACA,MAAMlD,iBAAiBd,GAAG,CAACa,UAAU;wBACnChD,MAAMtF,qBAAqB2K,KAAK;wBAChC1C,YAAY9B;wBACZhC;wBACAyE;wBACArH;wBACAmK,QAAQ,EAAE/E,gCAAAA,aAAcpF,IAAI;oBAC9B;oBAEJ,IAAIuG,0BAA0B;wBAC5B,yFAAyF;wBACzF,+FAA+F;wBAC/F,oDAAoD;wBACpD,IAAIhD,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;4BACvD,MAAM/E;wBACR;oBACF;oBAEA,IAAIuL,OAAO;wBACT,MAAM3C;oBACR,OAAO;wBACL,4HAA4H;wBAC5HK,sBAAsB;oBACxB;oBAEA,IAAIsC,CAAAA,yBAAAA,MAAOtF,KAAK,KAAIsF,MAAMtF,KAAK,CAACZ,IAAI,KAAKvF,gBAAgB4K,KAAK,EAAE;wBAC9D,wDAAwD;wBACxD,gDAAgD;wBAChD,IAAIrI,UAAUqJ,YAAY,IAAIH,MAAMvC,OAAO,EAAE;4BAC3CkC,yBAAyB;wBAC3B,OAAO;4BACL,IAAIK,MAAMvC,OAAO,EAAE;gCACjB3G,UAAUsJ,kBAAkB,KAAK,CAAC;gCAClC,IAAI,CAACtJ,UAAUsJ,kBAAkB,CAACtD,SAAS,EAAE;oCAC3C,MAAMuD,oBAAoB7C,gBAAgB,MACvCY,IAAI,CAAC,OAAOkC,WAAc,CAAA;4CACzBxC,MAAM,MAAMwC,SAAS5B,WAAW;4CAChCE,SAAS0B,SAAS1B,OAAO;4CACzBJ,QAAQ8B,SAAS9B,MAAM;4CACvBc,YAAYgB,SAAShB,UAAU;wCACjC,CAAA,GACCI,OAAO,CAAC;wCACP5I,UAAUsJ,kBAAkB,KAAK,CAAC;wCAClC,OAAOtJ,UAAUsJ,kBAAkB,CAACtD,YAAY,GAAG;oCACrD;oCAEF,2DAA2D;oCAC3D,8BAA8B;oCAC9BuD,kBAAkBZ,KAAK,CAACjJ,QAAQ2G,KAAK;oCAErCrG,UAAUsJ,kBAAkB,CAACtD,SAAS,GAAGuD;gCAC3C;4BACF;4BAEAR,kBAAkBG,MAAMtF,KAAK,CAAC0E,IAAI;wBACpC;oBACF;gBACF;gBAEA,IAAIS,iBAAiB;oBACnB,IAAIzG,YAAY;wBACdvC,iBAAiBC,WAAW;4BAC1BwH,OAAOlF;4BACPd,KAAKK;4BACL8C;4BACA8C,aAAaqB,oBAAoB,QAAQ;4BACzClE;4BACA8C,QAAQqB,gBAAgBrB,MAAM,IAAI;4BAClC3F,QAAQR,CAAAA,wBAAAA,KAAMQ,MAAM,KAAI;wBAC1B;oBACF;oBAEA,MAAMyH,WAAW,IAAIjB,SACnBL,OAAOC,IAAI,CAACY,gBAAgB/B,IAAI,EAAE,WAClC;wBACEc,SAASiB,gBAAgBjB,OAAO;wBAChCJ,QAAQqB,gBAAgBrB,MAAM;oBAChC;oBAGFK,OAAO0B,cAAc,CAACD,UAAU,OAAO;wBACrC5F,OAAOmF,gBAAgBvH,GAAG;oBAC5B;oBAEA,OAAOgI;gBACT;YACF;YAEA,IAAIxJ,UAAUO,kBAAkB,IAAIgB,QAAQ,OAAOA,SAAS,UAAU;gBACpE,MAAM,EAAEmI,KAAK,EAAE,GAAGnI;gBAElB,oEAAoE;gBACpE,IAAI1D,eAAe,OAAO0D,KAAKmI,KAAK;gBAEpC,IAAIA,UAAU,YAAY;oBACxB,uDAAuD;oBACvD,IAAIlH,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;wBACvD,IAAID,aAAa;4BACfA,YAAYmD,OAAO;4BACnBnD,cAAc;wBAChB;wBACA,OAAOlF,mBACLiF,cAAcqD,YAAY,EAC1B;oBAEJ,OAAO;wBACLvI,0BACE0C,WACAwC,eACA,CAAC,eAAe,EAAElB,MAAM,CAAC,EAAEtB,UAAUxB,KAAK,EAAE;oBAEhD;gBACF;gBAEA,MAAMmL,gBAAgB,UAAUpI;gBAChC,MAAM,EAAEW,OAAO,CAAC,CAAC,EAAE,GAAGX;gBACtB,IACE,OAAOW,KAAKyD,UAAU,KAAK,YAC3B1B,mBACA/B,KAAKyD,UAAU,GAAG1B,gBAAgB0B,UAAU,EAC5C;oBACA,IAAIzD,KAAKyD,UAAU,KAAK,GAAG;wBACzB,uDAAuD;wBACvD,IAAInD,iBAAiBA,cAAcE,IAAI,KAAK,aAAa;4BACvD,OAAOnF,mBACLiF,cAAcqD,YAAY,EAC1B;wBAEJ,OAAO;4BACLvI,0BACE0C,WACAwC,eACA,CAAC,oBAAoB,EAAElB,MAAM,CAAC,EAAEtB,UAAUxB,KAAK,EAAE;wBAErD;oBACF;oBAEA,IAAI,CAACwB,UAAU8F,WAAW,IAAI5D,KAAKyD,UAAU,KAAK,GAAG;wBACnD1B,gBAAgB0B,UAAU,GAAGzD,KAAKyD,UAAU;oBAC9C;gBACF;gBACA,IAAIgE,eAAe,OAAOpI,KAAKW,IAAI;YACrC;YAEA,kEAAkE;YAClE,6DAA6D;YAC7D,wCAAwC;YACxC,IAAI8D,YAAY6C,wBAAwB;gBACtC,MAAMe,uBAAuB5D;gBAC7BhG,UAAUsJ,kBAAkB,KAAK,CAAC;gBAClC,IAAIC,oBACFvJ,UAAUsJ,kBAAkB,CAACM,qBAAqB;gBAEpD,IAAIL,mBAAmB;oBACrB,MAAMM,oBAKF,MAAMN;oBACV,OAAO,IAAIhB,SAASsB,kBAAkB7C,IAAI,EAAE;wBAC1Cc,SAAS+B,kBAAkB/B,OAAO;wBAClCJ,QAAQmC,kBAAkBnC,MAAM;wBAChCc,YAAYqB,kBAAkBrB,UAAU;oBAC1C;gBACF;gBAEA,gEAAgE;gBAChE,sEAAsE;gBACtE,sEAAsE;gBACtE,sEAAsE;gBACtE,oEAAoE;gBACpE,mEAAmE;gBACnE,iEAAiE;gBACjE,uCAAuC;gBACvC,MAAMsB,kBAAkBpD,gBAAgB,MAAME,oBAC5C,8DAA8D;gBAC9D,8DAA8D;gBAC9D,mDAAmD;gBACnD,+CAA+C;iBAC9CU,IAAI,CAAC1J;gBAER2L,oBAAoBO,gBACjBxC,IAAI,CAAC,OAAOyC;oBACX,MAAMP,WAAWO,SAAS,CAAC,EAAE;oBAC7B,OAAO;wBACL/C,MAAM,MAAMwC,SAAS5B,WAAW;wBAChCE,SAAS0B,SAAS1B,OAAO;wBACzBJ,QAAQ8B,SAAS9B,MAAM;wBACvBc,YAAYgB,SAAShB,UAAU;oBACjC;gBACF,GACCI,OAAO,CAAC;wBAGF5I;oBAFL,8DAA8D;oBAC9D,6BAA6B;oBAC7B,IAAI,GAACA,gCAAAA,UAAUsJ,kBAAkB,qBAA5BtJ,6BAA8B,CAAC4J,qBAAqB,GAAE;wBACzD;oBACF;oBAEA,OAAO5J,UAAUsJ,kBAAkB,CAACM,qBAAqB;gBAC3D;gBAEF,mEAAmE;gBACnE,qBAAqB;gBACrBL,kBAAkBZ,KAAK,CAAC,KAAO;gBAE/B3I,UAAUsJ,kBAAkB,CAACM,qBAAqB,GAAGL;gBAErD,OAAOO,gBAAgBxC,IAAI,CAAC,CAACyC,YAAcA,SAAS,CAAC,EAAE;YACzD,OAAO;gBACL,OAAOrD,gBAAgB,OAAOE;YAChC;QACF;QAGF,IAAInE,aAAa;YACf,IAAI;gBACF,OAAO,MAAMG;YACf,SAAU;gBACR,IAAIH,aAAa;oBACfA,YAAYmD,OAAO;gBACrB;YACF;QACF;QACA,OAAOhD;IACT;IAEA,iEAAiE;IACjE,yEAAyE;IACzE,yEAAyE;IACzE,WAAW;IACXvB,QAAQ2I,aAAa,GAAG;IACxB3I,QAAQ4I,oBAAoB,GAAG,IAAM9I;IACrCE,QAAQ6I,kBAAkB,GAAGhJ;IAC3B7C,UAAsC,CAACJ,kBAAkB,GAAG;IAE9D,OAAOoD;AACT;AACA,uDAAuD;AACvD,yCAAyC;AACzC,OAAO,SAAS8I,WAAWC,OAAwB;IACjD,gEAAgE;IAChE,IAAIhM,kBAAkB;IAEtB,0EAA0E;IAC1E,8BAA8B;IAC9B,MAAMiM,WAAW7M,kBAAkBa,WAAW0E,KAAK;IAEnD,6CAA6C;IAC7C1E,WAAW0E,KAAK,GAAG9B,qBAAqBoJ,UAAUD;AACpD"}