{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/omit.ts"], "sourcesContent": ["export function omit<T extends { [key: string]: unknown }, K extends keyof T>(\n  object: T,\n  keys: K[]\n): Omit<T, K> {\n  const omitted: { [key: string]: unknown } = {}\n  Object.keys(object).forEach((key) => {\n    if (!keys.includes(key as K)) {\n      omitted[key] = object[key]\n    }\n  })\n  return omitted as Omit<T, K>\n}\n"], "names": ["omit", "object", "keys", "omitted", "Object", "for<PERSON>ach", "key", "includes"], "mappings": "AAAA,OAAO,SAASA,KACdC,MAAS,EACTC,IAAS;IAET,MAAMC,UAAsC,CAAC;IAC7CC,OAAOF,IAAI,CAACD,QAAQI,OAAO,CAAC,CAACC;QAC3B,IAAI,CAACJ,KAAKK,QAAQ,CAACD,MAAW;YAC5BH,OAAO,CAACG,IAAI,GAAGL,MAAM,CAACK,IAAI;QAC5B;IACF;IACA,OAAOH;AACT"}