{"version": 3, "sources": ["../../../src/server/lib/patch-set-header.ts"], "sourcesContent": ["import { getRequestMeta, type NextIncomingMessage } from '../request-meta'\n\ntype PatchableResponse = {\n  setHeader(key: string, value: string | string[]): PatchableResponse\n}\n\n/**\n * Ensure cookies set in middleware are merged and not overridden by API\n * routes/getServerSideProps.\n *\n * @param req Incoming request\n * @param res Outgoing response\n */\nexport function patchSetHeaderWithCookieSupport(\n  req: NextIncomingMessage,\n  res: PatchableResponse\n) {\n  const setHeader = res.setHeader.bind(res)\n  res.setHeader = (\n    name: string,\n    value: string | string[]\n  ): PatchableResponse => {\n    // When renders /_error after page is failed, it could attempt to set\n    // headers after headers.\n    if ('headersSent' in res && res.headersSent) {\n      return res\n    }\n\n    if (name.toLowerCase() === 'set-cookie') {\n      const middlewareValue = getRequestMeta(req, 'middlewareCookie')\n\n      if (\n        !middlewareValue ||\n        !Array.isArray(value) ||\n        !value.every((item, idx) => item === middlewareValue[idx])\n      ) {\n        value = [\n          // TODO: (wyattjoh) find out why this is called multiple times resulting in duplicate cookies being added\n          ...new Set([\n            ...(middlewareValue || []),\n            ...(typeof value === 'string'\n              ? [value]\n              : Array.isArray(value)\n                ? value\n                : []),\n          ]),\n        ]\n      }\n    }\n\n    return setHeader(name, value)\n  }\n}\n"], "names": ["patchSetHeaderWithCookieSupport", "req", "res", "<PERSON><PERSON><PERSON><PERSON>", "bind", "name", "value", "headersSent", "toLowerCase", "middlewareValue", "getRequestMeta", "Array", "isArray", "every", "item", "idx", "Set"], "mappings": ";;;;+BAagBA;;;eAAAA;;;6BAbyC;AAalD,SAASA,gCACdC,GAAwB,EACxBC,GAAsB;IAEtB,MAAMC,YAAYD,IAAIC,SAAS,CAACC,IAAI,CAACF;IACrCA,IAAIC,SAAS,GAAG,CACdE,MACAC;QAEA,qEAAqE;QACrE,yBAAyB;QACzB,IAAI,iBAAiBJ,OAAOA,IAAIK,WAAW,EAAE;YAC3C,OAAOL;QACT;QAEA,IAAIG,KAAKG,WAAW,OAAO,cAAc;YACvC,MAAMC,kBAAkBC,IAAAA,2BAAc,EAACT,KAAK;YAE5C,IACE,CAACQ,mBACD,CAACE,MAAMC,OAAO,CAACN,UACf,CAACA,MAAMO,KAAK,CAAC,CAACC,MAAMC,MAAQD,SAASL,eAAe,CAACM,IAAI,GACzD;gBACAT,QAAQ;oBACN,yGAAyG;uBACtG,IAAIU,IAAI;2BACLP,mBAAmB,EAAE;2BACrB,OAAOH,UAAU,WACjB;4BAACA;yBAAM,GACPK,MAAMC,OAAO,CAACN,SACZA,QACA,EAAE;qBACT;iBACF;YACH;QACF;QAEA,OAAOH,UAAUE,MAAMC;IACzB;AACF"}