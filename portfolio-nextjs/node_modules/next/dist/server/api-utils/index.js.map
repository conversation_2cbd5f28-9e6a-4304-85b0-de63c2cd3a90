{"version": 3, "sources": ["../../../src/server/api-utils/index.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { NextApiResponse } from '../../shared/lib/utils'\n\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../lib/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NodeSpan } from '../lib/trace/constants'\n\nexport type NextApiRequestCookies = Partial<{ [key: string]: string }>\nexport type NextApiRequestQuery = Partial<{ [key: string]: string | string[] }>\n\nexport type __ApiPreviewProps = {\n  previewModeId: string\n  previewModeEncryptionKey: string\n  previewModeSigningKey: string\n}\n\nexport function wrapApiHandler<T extends (...args: any[]) => any>(\n  page: string,\n  handler: T\n): T {\n  return ((...args) => {\n    getTracer().setRootSpanAttribute('next.route', page)\n    // Call API route method\n    return getTracer().trace(\n      NodeSpan.runHandler,\n      {\n        spanName: `executing api route (pages) ${page}`,\n      },\n      () => handler(...args)\n    )\n  }) as T\n}\n\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */\nexport function sendStatusCode(\n  res: NextApiResponse,\n  statusCode: number\n): NextApiResponse<any> {\n  res.statusCode = statusCode\n  return res\n}\n\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */\nexport function redirect(\n  res: NextApiResponse,\n  statusOrUrl: string | number,\n  url?: string\n): NextApiResponse<any> {\n  if (typeof statusOrUrl === 'string') {\n    url = statusOrUrl\n    statusOrUrl = 307\n  }\n  if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n    throw new Error(\n      `Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`\n    )\n  }\n  res.writeHead(statusOrUrl, { Location: url })\n  res.write(url)\n  res.end()\n  return res\n}\n\nexport function checkIsOnDemandRevalidate(\n  req: Request | IncomingMessage | BaseNextRequest,\n  previewProps: __ApiPreviewProps\n): {\n  isOnDemandRevalidate: boolean\n  revalidateOnlyGenerated: boolean\n} {\n  const headers = HeadersAdapter.from(req.headers)\n\n  const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER)\n  const isOnDemandRevalidate = previewModeId === previewProps.previewModeId\n\n  const revalidateOnlyGenerated = headers.has(\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER\n  )\n\n  return { isOnDemandRevalidate, revalidateOnlyGenerated }\n}\n\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`\n\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024\n\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA)\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS)\n\nexport function clearPreviewData<T>(\n  res: NextApiResponse<T>,\n  options: {\n    path?: string\n  } = {}\n): NextApiResponse<T> {\n  if (SYMBOL_CLEARED_COOKIES in res) {\n    return res\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n\n  Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n    value: true,\n    enumerable: false,\n  })\n  return res\n}\n\n/**\n * Custom error class\n */\nexport class ApiError extends Error {\n  readonly statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n    this.statusCode = statusCode\n  }\n}\n\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */\nexport function sendError(\n  res: NextApiResponse,\n  statusCode: number,\n  message: string\n): void {\n  res.statusCode = statusCode\n  res.statusMessage = message\n  res.end(message)\n}\n\ninterface LazyProps {\n  req: IncomingMessage\n}\n\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */\nexport function setLazyProp<T>(\n  { req }: LazyProps,\n  prop: string,\n  getter: () => T\n): void {\n  const opts = { configurable: true, enumerable: true }\n  const optsReset = { ...opts, writable: true }\n\n  Object.defineProperty(req, prop, {\n    ...opts,\n    get: () => {\n      const value = getter()\n      // we set the property on the object to avoid recalculating it\n      Object.defineProperty(req, prop, { ...optsReset, value })\n      return value\n    },\n    set: (value) => {\n      Object.defineProperty(req, prop, { ...optsReset, value })\n    },\n  })\n}\n"], "names": ["ApiError", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "RESPONSE_LIMIT_DEFAULT", "SYMBOL_CLEARED_COOKIES", "SYMBOL_PREVIEW_DATA", "checkIsOnDemandRevalidate", "clearPreviewData", "redirect", "sendError", "sendStatusCode", "setLazyProp", "wrapApiHandler", "page", "handler", "args", "getTracer", "setRootSpanAttribute", "trace", "NodeSpan", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "res", "statusCode", "statusOrUrl", "url", "Error", "writeHead", "Location", "write", "end", "req", "previewProps", "headers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "previewModeId", "get", "PRERENDER_REVALIDATE_HEADER", "isOnDemandRevalidate", "revalidateOnlyGenerated", "has", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "Symbol", "options", "serialize", "require", "previous", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "expires", "Date", "httpOnly", "sameSite", "process", "env", "NODE_ENV", "secure", "path", "undefined", "Object", "defineProperty", "value", "enumerable", "constructor", "message", "statusMessage", "prop", "getter", "opts", "configurable", "optsReset", "writable", "set"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IAkKaA,QAAQ;eAARA;;IAjEAC,4BAA4B;eAA5BA;;IACAC,0BAA0B;eAA1BA;;IAEAC,sBAAsB;eAAtBA;;IAGAC,sBAAsB;eAAtBA;;IADAC,mBAAmB;eAAnBA;;IAxBGC,yBAAyB;eAAzBA;;IA2BAC,gBAAgB;eAAhBA;;IA/CAC,QAAQ;eAARA;;IAuHAC,SAAS;eAATA;;IArIAC,cAAc;eAAdA;;IAyJAC,WAAW;eAAXA;;IA/KAC,cAAc;eAAdA;;;yBAjBe;2BAIxB;wBACmB;4BACD;AAWlB,SAASA,eACdC,IAAY,EACZC,OAAU;IAEV,OAAQ,CAAC,GAAGC;QACVC,IAAAA,iBAAS,IAAGC,oBAAoB,CAAC,cAAcJ;QAC/C,wBAAwB;QACxB,OAAOG,IAAAA,iBAAS,IAAGE,KAAK,CACtBC,oBAAQ,CAACC,UAAU,EACnB;YACEC,UAAU,CAAC,4BAA4B,EAAER,MAAM;QACjD,GACA,IAAMC,WAAWC;IAErB;AACF;AAOO,SAASL,eACdY,GAAoB,EACpBC,UAAkB;IAElBD,IAAIC,UAAU,GAAGA;IACjB,OAAOD;AACT;AAQO,SAASd,SACdc,GAAoB,EACpBE,WAA4B,EAC5BC,GAAY;IAEZ,IAAI,OAAOD,gBAAgB,UAAU;QACnCC,MAAMD;QACNA,cAAc;IAChB;IACA,IAAI,OAAOA,gBAAgB,YAAY,OAAOC,QAAQ,UAAU;QAC9D,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,qKAAqK,CAAC,GADnK,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAJ,IAAIK,SAAS,CAACH,aAAa;QAAEI,UAAUH;IAAI;IAC3CH,IAAIO,KAAK,CAACJ;IACVH,IAAIQ,GAAG;IACP,OAAOR;AACT;AAEO,SAAShB,0BACdyB,GAAgD,EAChDC,YAA+B;IAK/B,MAAMC,UAAUC,uBAAc,CAACC,IAAI,CAACJ,IAAIE,OAAO;IAE/C,MAAMG,gBAAgBH,QAAQI,GAAG,CAACC,sCAA2B;IAC7D,MAAMC,uBAAuBH,kBAAkBJ,aAAaI,aAAa;IAEzE,MAAMI,0BAA0BP,QAAQQ,GAAG,CACzCC,qDAA0C;IAG5C,OAAO;QAAEH;QAAsBC;IAAwB;AACzD;AAEO,MAAMvC,+BAA+B,CAAC,kBAAkB,CAAC;AACzD,MAAMC,6BAA6B,CAAC,mBAAmB,CAAC;AAExD,MAAMC,yBAAyB,IAAI,OAAO;AAE1C,MAAME,sBAAsBsC,OAAOzC;AACnC,MAAME,yBAAyBuC,OAAO1C;AAEtC,SAASM,iBACde,GAAuB,EACvBsB,UAEI,CAAC,CAAC;IAEN,IAAIxC,0BAA0BkB,KAAK;QACjC,OAAOA;IACT;IAEA,MAAM,EAAEuB,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAWzB,IAAI0B,SAAS,CAAC;IAC/B1B,IAAI2B,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAOF,aAAa,WACpB;YAACA;SAAS,GACVG,MAAMC,OAAO,CAACJ,YACZA,WACA,EAAE;QACRF,UAAU5C,8BAA8B,IAAI;YAC1C,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEmD,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;QACAhB,UAAU3C,4BAA4B,IAAI;YACxC,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEkD,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;KACD;IAEDC,OAAOC,cAAc,CAACzC,KAAKlB,wBAAwB;QACjD4D,OAAO;QACPC,YAAY;IACd;IACA,OAAO3C;AACT;AAKO,MAAMtB,iBAAiB0B;IAG5BwC,YAAY3C,UAAkB,EAAE4C,OAAe,CAAE;QAC/C,KAAK,CAACA;QACN,IAAI,CAAC5C,UAAU,GAAGA;IACpB;AACF;AAQO,SAASd,UACda,GAAoB,EACpBC,UAAkB,EAClB4C,OAAe;IAEf7C,IAAIC,UAAU,GAAGA;IACjBD,IAAI8C,aAAa,GAAGD;IACpB7C,IAAIQ,GAAG,CAACqC;AACV;AAYO,SAASxD,YACd,EAAEoB,GAAG,EAAa,EAClBsC,IAAY,EACZC,MAAe;IAEf,MAAMC,OAAO;QAAEC,cAAc;QAAMP,YAAY;IAAK;IACpD,MAAMQ,YAAY;QAAE,GAAGF,IAAI;QAAEG,UAAU;IAAK;IAE5CZ,OAAOC,cAAc,CAAChC,KAAKsC,MAAM;QAC/B,GAAGE,IAAI;QACPlC,KAAK;YACH,MAAM2B,QAAQM;YACd,8DAA8D;YAC9DR,OAAOC,cAAc,CAAChC,KAAKsC,MAAM;gBAAE,GAAGI,SAAS;gBAAET;YAAM;YACvD,OAAOA;QACT;QACAW,KAAK,CAACX;YACJF,OAAOC,cAAc,CAAChC,KAAKsC,MAAM;gBAAE,GAAGI,SAAS;gBAAET;YAAM;QACzD;IACF;AACF"}