{"version": 3, "sources": ["../../../src/server/web/internal-edge-wait-until.ts"], "sourcesContent": ["// An internal module to expose the \"waitUntil\" API to Edge SSR and Edge Route Handler functions.\n// This is highly experimental and subject to change.\n\n// We still need a global key to bypass Webpack's layering of modules.\nconst GLOBAL_KEY = Symbol.for('__next_internal_waitUntil__')\n\nconst state: {\n  waitUntilCounter: number\n  waitUntilResolve: () => void\n  waitUntilPromise: Promise<void> | null\n} =\n  // @ts-ignore\n  globalThis[GLOBAL_KEY] ||\n  // @ts-ignore\n  (globalThis[GLOBAL_KEY] = {\n    waitUntilCounter: 0,\n    waitUntilResolve: undefined,\n    waitUntilPromise: null,\n  })\n\n// No matter how many concurrent requests are being handled, we want to make sure\n// that the final promise is only resolved once all of the waitUntil promises have\n// settled.\nfunction resolveOnePromise() {\n  state.waitUntilCounter--\n  if (state.waitUntilCounter === 0) {\n    state.waitUntilResolve()\n    state.waitUntilPromise = null\n  }\n}\n\nexport function internal_getCurrentFunctionWaitUntil() {\n  return state.waitUntilPromise\n}\n\nexport function internal_runWithWaitUntil<T>(fn: () => T): T {\n  const result = fn()\n  if (\n    result &&\n    typeof result === 'object' &&\n    'then' in result &&\n    'finally' in result &&\n    typeof result.then === 'function' &&\n    typeof result.finally === 'function'\n  ) {\n    if (!state.waitUntilCounter) {\n      // Create the promise for the next batch of waitUntil calls.\n      state.waitUntilPromise = new Promise<void>((resolve) => {\n        state.waitUntilResolve = resolve\n      })\n    }\n    state.waitUntilCounter++\n    return result.finally(() => {\n      resolveOnePromise()\n    })\n  }\n\n  return result\n}\n"], "names": ["internal_getCurrentFunctionWaitUntil", "internal_runWithWaitUntil", "GLOBAL_KEY", "Symbol", "for", "state", "globalThis", "waitUntilCounter", "waitUntilResolve", "undefined", "waitUntilPromise", "resolveOnePromise", "fn", "result", "then", "finally", "Promise", "resolve"], "mappings": "AAAA,iGAAiG;AACjG,qDAAqD;AAErD,sEAAsE;;;;;;;;;;;;;;;;IA4BtDA,oCAAoC;eAApCA;;IAIAC,yBAAyB;eAAzBA;;;AA/BhB,MAAMC,aAAaC,OAAOC,GAAG,CAAC;AAE9B,MAAMC,QAKJ,aAAa;AACbC,UAAU,CAACJ,WAAW,IACtB,aAAa;AACZI,CAAAA,UAAU,CAACJ,WAAW,GAAG;IACxBK,kBAAkB;IAClBC,kBAAkBC;IAClBC,kBAAkB;AACpB,CAAA;AAEF,iFAAiF;AACjF,kFAAkF;AAClF,WAAW;AACX,SAASC;IACPN,MAAME,gBAAgB;IACtB,IAAIF,MAAME,gBAAgB,KAAK,GAAG;QAChCF,MAAMG,gBAAgB;QACtBH,MAAMK,gBAAgB,GAAG;IAC3B;AACF;AAEO,SAASV;IACd,OAAOK,MAAMK,gBAAgB;AAC/B;AAEO,SAAST,0BAA6BW,EAAW;IACtD,MAAMC,SAASD;IACf,IACEC,UACA,OAAOA,WAAW,YAClB,UAAUA,UACV,aAAaA,UACb,OAAOA,OAAOC,IAAI,KAAK,cACvB,OAAOD,OAAOE,OAAO,KAAK,YAC1B;QACA,IAAI,CAACV,MAAME,gBAAgB,EAAE;YAC3B,4DAA4D;YAC5DF,MAAMK,gBAAgB,GAAG,IAAIM,QAAc,CAACC;gBAC1CZ,MAAMG,gBAAgB,GAAGS;YAC3B;QACF;QACAZ,MAAME,gBAAgB;QACtB,OAAOM,OAAOE,OAAO,CAAC;YACpBJ;QACF;IACF;IAEA,OAAOE;AACT"}