{"version": 3, "sources": ["../../src/server/load-manifest.ts"], "sourcesContent": ["import type { DeepReadonly } from '../shared/lib/deep-readonly'\n\nimport { readFileSync } from 'fs'\nimport { runInNewContext } from 'vm'\nimport { deepFreeze } from '../shared/lib/deep-freeze'\n\nconst sharedCache = new Map<string, unknown>()\n\n/**\n * Load a manifest file from the file system. Optionally cache the manifest in\n * memory to avoid reading the file multiple times using the provided cache or\n * defaulting to a shared module cache. The manifest is frozen to prevent\n * modifications if it is cached.\n *\n * @param path the path to the manifest file\n * @param shouldCache whether to cache the manifest in memory\n * @param cache the cache to use for storing the manifest\n * @returns the manifest object\n */\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache: false\n): T\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache?: boolean,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache?: true,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache: boolean = true,\n  cache = sharedCache\n): T {\n  const cached = shouldCache && cache.get(path)\n  if (cached) {\n    return cached as T\n  }\n\n  let manifest = JSON.parse(readFileSync(path, 'utf8'))\n\n  // Freeze the manifest so it cannot be modified if we're caching it.\n  if (shouldCache) {\n    manifest = deepFreeze(manifest)\n  }\n\n  if (shouldCache) {\n    cache.set(path, manifest)\n  }\n\n  return manifest\n}\n\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache: false\n): T\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache?: boolean,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache?: true,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache: boolean = true,\n  cache = sharedCache\n): T {\n  const cached = shouldCache && cache.get(path)\n  if (cached) {\n    return cached as T\n  }\n\n  const content = readFileSync(path, 'utf8')\n  if (content.length === 0) {\n    throw new Error('Manifest file is empty')\n  }\n\n  let contextObject = {}\n  runInNewContext(content, contextObject)\n\n  // Freeze the context object so it cannot be modified if we're caching it.\n  if (shouldCache) {\n    contextObject = deepFreeze(contextObject)\n  }\n\n  if (shouldCache) {\n    cache.set(path, contextObject)\n  }\n\n  return contextObject as T\n}\n\nexport function clearManifestCache(path: string, cache = sharedCache): boolean {\n  return cache.delete(path)\n}\n"], "names": ["clearManifestCache", "evalManifest", "loadManifest", "sharedCache", "Map", "path", "shouldCache", "cache", "cached", "get", "manifest", "JSON", "parse", "readFileSync", "deepFreeze", "set", "content", "length", "Error", "contextObject", "runInNewContext", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAqGgBA,kBAAkB;eAAlBA;;IA9BAC,YAAY;eAAZA;;IAtCAC,YAAY;eAAZA;;;oBA/Ba;oBACG;4BACL;AAE3B,MAAMC,cAAc,IAAIC;AA2BjB,SAASF,aACdG,IAAY,EACZC,cAAuB,IAAI,EAC3BC,QAAQJ,WAAW;IAEnB,MAAMK,SAASF,eAAeC,MAAME,GAAG,CAACJ;IACxC,IAAIG,QAAQ;QACV,OAAOA;IACT;IAEA,IAAIE,WAAWC,KAAKC,KAAK,CAACC,IAAAA,gBAAY,EAACR,MAAM;IAE7C,oEAAoE;IACpE,IAAIC,aAAa;QACfI,WAAWI,IAAAA,sBAAU,EAACJ;IACxB;IAEA,IAAIJ,aAAa;QACfC,MAAMQ,GAAG,CAACV,MAAMK;IAClB;IAEA,OAAOA;AACT;AAgBO,SAAST,aACdI,IAAY,EACZC,cAAuB,IAAI,EAC3BC,QAAQJ,WAAW;IAEnB,MAAMK,SAASF,eAAeC,MAAME,GAAG,CAACJ;IACxC,IAAIG,QAAQ;QACV,OAAOA;IACT;IAEA,MAAMQ,UAAUH,IAAAA,gBAAY,EAACR,MAAM;IACnC,IAAIW,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,qBAAmC,CAAnC,IAAIC,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,IAAIC,gBAAgB,CAAC;IACrBC,IAAAA,mBAAe,EAACJ,SAASG;IAEzB,0EAA0E;IAC1E,IAAIb,aAAa;QACfa,gBAAgBL,IAAAA,sBAAU,EAACK;IAC7B;IAEA,IAAIb,aAAa;QACfC,MAAMQ,GAAG,CAACV,MAAMc;IAClB;IAEA,OAAOA;AACT;AAEO,SAASnB,mBAAmBK,IAAY,EAAEE,QAAQJ,WAAW;IAClE,OAAOI,MAAMc,MAAM,CAAChB;AACtB"}