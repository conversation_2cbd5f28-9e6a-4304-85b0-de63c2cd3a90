{"version": 3, "sources": ["../../../src/server/dev/require-cache.ts"], "sourcesContent": ["import isError from '../../lib/is-error'\nimport { realpathSync } from '../../lib/realpath'\nimport { clearManifestCache } from '../load-manifest'\n\nexport function deleteFromRequireCache(filePath: string) {\n  try {\n    filePath = realpathSync(filePath)\n  } catch (e) {\n    if (isError(e) && e.code !== 'ENOENT') throw e\n  }\n  const mod = require.cache[filePath]\n  if (mod) {\n    // remove the child reference from all parent modules\n    for (const parent of Object.values(require.cache)) {\n      if (parent?.children) {\n        const idx = parent.children.indexOf(mod)\n        if (idx >= 0) parent.children.splice(idx, 1)\n      }\n    }\n    // remove parent references from external modules\n    for (const child of mod.children) {\n      child.parent = null\n    }\n    delete require.cache[filePath]\n    return true\n  }\n  return false\n}\n\nexport function deleteCache(filePath: string) {\n  // try to clear it from the fs cache\n  clearManifestCache(filePath)\n\n  deleteFromRequireCache(filePath)\n}\n"], "names": ["deleteCache", "deleteFromRequireCache", "filePath", "realpathSync", "e", "isError", "code", "mod", "require", "cache", "parent", "Object", "values", "children", "idx", "indexOf", "splice", "child", "clearManifestCache"], "mappings": ";;;;;;;;;;;;;;;IA6BgBA,WAAW;eAAXA;;IAzBAC,sBAAsB;eAAtBA;;;gEAJI;0BACS;8BACM;;;;;;AAE5B,SAASA,uBAAuBC,QAAgB;IACrD,IAAI;QACFA,WAAWC,IAAAA,sBAAY,EAACD;IAC1B,EAAE,OAAOE,GAAG;QACV,IAAIC,IAAAA,gBAAO,EAACD,MAAMA,EAAEE,IAAI,KAAK,UAAU,MAAMF;IAC/C;IACA,MAAMG,MAAMC,QAAQC,KAAK,CAACP,SAAS;IACnC,IAAIK,KAAK;QACP,qDAAqD;QACrD,KAAK,MAAMG,UAAUC,OAAOC,MAAM,CAACJ,QAAQC,KAAK,EAAG;YACjD,IAAIC,0BAAAA,OAAQG,QAAQ,EAAE;gBACpB,MAAMC,MAAMJ,OAAOG,QAAQ,CAACE,OAAO,CAACR;gBACpC,IAAIO,OAAO,GAAGJ,OAAOG,QAAQ,CAACG,MAAM,CAACF,KAAK;YAC5C;QACF;QACA,iDAAiD;QACjD,KAAK,MAAMG,SAASV,IAAIM,QAAQ,CAAE;YAChCI,MAAMP,MAAM,GAAG;QACjB;QACA,OAAOF,QAAQC,KAAK,CAACP,SAAS;QAC9B,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAASF,YAAYE,QAAgB;IAC1C,oCAAoC;IACpCgB,IAAAA,gCAAkB,EAAChB;IAEnBD,uBAAuBC;AACzB"}