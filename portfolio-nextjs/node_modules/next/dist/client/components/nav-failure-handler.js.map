{"version": 3, "sources": ["../../../src/client/components/nav-failure-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n"], "names": ["handleHardNavError", "useNavFailureHandler", "error", "window", "next", "__pendingUrl", "createHrefFromUrl", "URL", "location", "href", "console", "toString", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "useEffect", "uncaughtExceptionHandler", "evt", "reason", "addEventListener", "removeEventListener"], "mappings": ";;;;;;;;;;;;;;;IAGgBA,kBAAkB;eAAlBA;;IAkBAC,oBAAoB;eAApBA;;;uBArBU;mCACQ;AAE3B,SAASD,mBAAmBE,KAAc;IAC/C,IACEA,SACA,OAAOC,WAAW,eAClBA,OAAOC,IAAI,CAACC,YAAY,IACxBC,IAAAA,oCAAiB,EAAC,IAAIC,IAAIJ,OAAOK,QAAQ,CAACC,IAAI,OAC5CH,IAAAA,oCAAiB,EAACH,OAAOC,IAAI,CAACC,YAAY,GAC5C;QACAK,QAAQR,KAAK,CACV,qEACDA;QAEFC,OAAOK,QAAQ,CAACC,IAAI,GAAGN,OAAOC,IAAI,CAACC,YAAY,CAACM,QAAQ;QACxD,OAAO;IACT;IACA,OAAO;AACT;AAEO,SAASV;IACd,IAAIW,QAAQC,GAAG,CAACC,4BAA4B,EAAE;QAC5C,8DAA8D;QAC9D,sDAAsD;QACtDC,IAAAA,gBAAS,EAAC;YACR,MAAMC,2BAA2B,CAC/BC;gBAEA,MAAMf,QAAQ,YAAYe,MAAMA,IAAIC,MAAM,GAAGD,IAAIf,KAAK;gBACtD,qDAAqD;gBACrD,oDAAoD;gBACpD,qCAAqC;gBACrCF,mBAAmBE;YACrB;YACAC,OAAOgB,gBAAgB,CAAC,sBAAsBH;YAC9Cb,OAAOgB,gBAAgB,CAAC,SAASH;YACjC,OAAO;gBACLb,OAAOiB,mBAAmB,CAAC,SAASJ;gBACpCb,OAAOiB,mBAAmB,CACxB,sBACAJ;YAEJ;QACF,GAAG,EAAE;IACP;AACF"}