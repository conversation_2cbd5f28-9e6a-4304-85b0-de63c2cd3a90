{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/pages-dev-overlay-error-boundary.tsx"], "sourcesContent": ["import React from 'react'\n\ntype PagesDevOverlayErrorBoundaryProps = {\n  children?: React.ReactNode\n  onError: (error: Error, componentStack: string | null) => void\n}\ntype PagesDevOverlayErrorBoundaryState = { error: Error | null }\n\nexport class PagesDevOverlayErrorBoundary extends React.PureComponent<\n  PagesDevOverlayErrorBoundaryProps,\n  PagesDevOverlayErrorBoundaryState\n> {\n  state = { error: null }\n\n  static getDerivedStateFromError(error: Error) {\n    return { error }\n  }\n\n  componentDidCatch(\n    error: Error,\n    // Loosely typed because it depends on the React version and was\n    // accidentally excluded in some versions.\n    errorInfo?: { componentStack?: string | null }\n  ) {\n    this.props.onError(error, errorInfo?.componentStack || null)\n    this.setState({ error })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    // The component has to be unmounted or else it would continue to error\n    return this.state.error ? null : this.props.children\n  }\n}\n"], "names": ["PagesDevOverlayErrorBoundary", "React", "PureComponent", "getDerivedStateFromError", "error", "componentDidCatch", "errorInfo", "props", "onError", "componentStack", "setState", "render", "state", "children"], "mappings": ";;;;+BAQaA;;;eAAAA;;;;gEARK;AAQX,MAAMA,qCAAqCC,cAAK,CAACC,aAAa;IAMnE,OAAOC,yBAAyBC,KAAY,EAAE;QAC5C,OAAO;YAAEA;QAAM;IACjB;IAEAC,kBACED,KAAY,EACZ,gEAAgE;IAChE,0CAA0C;IAC1CE,SAA8C,EAC9C;QACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,OAAOE,CAAAA,6BAAAA,UAAWG,cAAc,KAAI;QACvD,IAAI,CAACC,QAAQ,CAAC;YAAEN;QAAM;IACxB;IAEA,yIAAyI;IACzIO,SAA0B;QACxB,uEAAuE;QACvE,OAAO,IAAI,CAACC,KAAK,CAACR,KAAK,GAAG,OAAO,IAAI,CAACG,KAAK,CAACM,QAAQ;IACtD;;QAxBK,qBAILD,QAAQ;YAAER,OAAO;QAAK;;AAqBxB"}