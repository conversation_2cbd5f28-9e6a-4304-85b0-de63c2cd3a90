{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/utils/parse-url-from-text.ts"], "sourcesContent": ["export function parseUrlFromText(\n  text: string,\n  matcherFunc?: (text: string) => boolean\n): string[] {\n  const linkRegex = /https?:\\/\\/[^\\s/$.?#].[^\\s)'\"]*/gi\n  const links = Array.from(text.matchAll(linkRegex), (match) => match[0])\n\n  if (matcherFunc) {\n    return links.filter((link) => matcherFunc(link))\n  }\n\n  return links\n}\n"], "names": ["parseUrlFromText", "text", "matcher<PERSON>unc", "linkRegex", "links", "Array", "from", "matchAll", "match", "filter", "link"], "mappings": ";;;;+BAAgBA;;;eAAAA;;;AAAT,SAASA,iBACdC,IAAY,EACZC,WAAuC;IAEvC,MAAMC,YAAY;IAClB,MAAMC,QAAQC,MAAMC,IAAI,CAACL,KAAKM,QAAQ,CAACJ,YAAY,CAACK,QAAUA,KAAK,CAAC,EAAE;IAEtE,IAAIN,aAAa;QACf,OAAOE,MAAMK,MAAM,CAAC,CAACC,OAASR,YAAYQ;IAC5C;IAEA,OAAON;AACT"}