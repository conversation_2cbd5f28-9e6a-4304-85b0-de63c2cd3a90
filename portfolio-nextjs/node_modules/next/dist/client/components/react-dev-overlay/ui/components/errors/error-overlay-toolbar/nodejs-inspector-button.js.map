{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/nodejs-inspector-button.tsx"], "sourcesContent": ["import { CopyButton } from '../../copy-button'\n\n// Inline this helper to avoid widely used across the codebase,\n// as for this feature the Chrome detector doesn't need to be super accurate.\nfunction isChrome() {\n  if (typeof window === 'undefined') return false\n  const isChromium = 'chrome' in window && window.chrome\n  const vendorName = window.navigator.vendor\n\n  return (\n    isChromium !== null &&\n    isChromium !== undefined &&\n    vendorName === 'Google Inc.'\n  )\n}\n\nconst isChromeBrowser = isChrome()\n\nfunction NodeJsIcon(props: any) {\n  return (\n    <svg\n      width=\"14\"\n      height=\"14\"\n      viewBox=\"0 0 14 14\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <mask\n        id=\"nodejs_icon_mask_a\"\n        style={{ maskType: 'luminance' }}\n        maskUnits=\"userSpaceOnUse\"\n        x=\"0\"\n        y=\"0\"\n        width=\"14\"\n        height=\"14\"\n      >\n        <path\n          d=\"M6.67.089 1.205 3.256a.663.663 0 0 0-.33.573v6.339c0 .237.126.455.33.574l5.466 3.17a.66.66 0 0 0 .66 0l5.465-3.17a.664.664 0 0 0 .329-.574V3.829a.663.663 0 0 0-.33-.573L7.33.089a.663.663 0 0 0-.661 0\"\n          fill=\"#fff\"\n        />\n      </mask>\n      <g mask=\"url(#nodejs_icon_mask_a)\">\n        <path\n          d=\"M18.648 2.717 3.248-4.86-4.648 11.31l15.4 7.58 7.896-16.174z\"\n          fill=\"url(#nodejs_icon_linear_gradient_b)\"\n        />\n      </g>\n      <mask\n        id=\"nodejs_icon_mask_c\"\n        style={{ maskType: 'luminance' }}\n        maskUnits=\"userSpaceOnUse\"\n        x=\"1\"\n        y=\"0\"\n        width=\"12\"\n        height=\"14\"\n      >\n        <path\n          d=\"M1.01 10.57a.663.663 0 0 0 .195.17l4.688 2.72.781.45a.66.66 0 0 0 .51.063l5.764-10.597a.653.653 0 0 0-.153-.122L9.216 1.18 7.325.087a.688.688 0 0 0-.171-.07L1.01 10.57z\"\n          fill=\"#fff\"\n        />\n      </mask>\n      <g mask=\"url(#nodejs_icon_mask_c)\">\n        <path\n          d=\"M-5.647 4.958 5.226 19.734l14.38-10.667L8.734-5.71-5.647 4.958z\"\n          fill=\"url(#nodejs_icon_linear_gradient_d)\"\n        />\n      </g>\n      <g>\n        <mask\n          id=\"nodejs_icon_mask_e\"\n          style={{ maskType: 'luminance' }}\n          maskUnits=\"userSpaceOnUse\"\n          x=\"1\"\n          y=\"0\"\n          width=\"13\"\n          height=\"14\"\n        >\n          <path\n            d=\"M6.934.004A.665.665 0 0 0 6.67.09L1.22 3.247l5.877 10.746a.655.655 0 0 0 .235-.08l5.465-3.17a.665.665 0 0 0 .319-.453L7.126.015a.684.684 0 0 0-.189-.01\"\n            fill=\"#fff\"\n          />\n        </mask>\n        <g mask=\"url(#nodejs_icon_mask_e)\">\n          <path\n            d=\"M1.22.002v13.992h11.894V.002H1.22z\"\n            fill=\"url(#nodejs_icon_linear_gradient_f)\"\n          />\n        </g>\n      </g>\n      <defs>\n        <linearGradient\n          id=\"nodejs_icon_linear_gradient_b\"\n          x1=\"10.943\"\n          y1=\"-1.084\"\n          x2=\"2.997\"\n          y2=\"15.062\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\".3\" stopColor=\"#3E863D\" />\n          <stop offset=\".5\" stopColor=\"#55934F\" />\n          <stop offset=\".8\" stopColor=\"#5AAD45\" />\n        </linearGradient>\n        <linearGradient\n          id=\"nodejs_icon_linear_gradient_d\"\n          x1=\"-.145\"\n          y1=\"12.431\"\n          x2=\"14.277\"\n          y2=\"1.818\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\".57\" stopColor=\"#3E863D\" />\n          <stop offset=\".72\" stopColor=\"#619857\" />\n          <stop offset=\"1\" stopColor=\"#76AC64\" />\n        </linearGradient>\n        <linearGradient\n          id=\"nodejs_icon_linear_gradient_f\"\n          x1=\"1.225\"\n          y1=\"6.998\"\n          x2=\"13.116\"\n          y2=\"6.998\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\".16\" stopColor=\"#6BBF47\" />\n          <stop offset=\".38\" stopColor=\"#79B461\" />\n          <stop offset=\".47\" stopColor=\"#75AC64\" />\n          <stop offset=\".7\" stopColor=\"#659E5A\" />\n          <stop offset=\".9\" stopColor=\"#3E863D\" />\n        </linearGradient>\n      </defs>\n    </svg>\n  )\n}\n\nfunction NodeJsDisabledIcon(props: any) {\n  return (\n    <svg\n      width=\"14\"\n      height=\"14\"\n      viewBox=\"0 0 14 14\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <mask\n        id=\"nodejs_icon_mask_a\"\n        style={{ maskType: 'luminance' }}\n        maskUnits=\"userSpaceOnUse\"\n        x=\"0\"\n        y=\"0\"\n        width=\"14\"\n        height=\"14\"\n      >\n        <path\n          d=\"M6.67.089 1.205 3.256a.663.663 0 0 0-.33.573v6.339c0 .237.126.455.33.574l5.466 3.17a.66.66 0 0 0 .66 0l5.465-3.17a.664.664 0 0 0 .329-.574V3.829a.663.663 0 0 0-.33-.573L7.33.089a.663.663 0 0 0-.661 0\"\n          fill=\"#fff\"\n        />\n      </mask>\n      <g mask=\"url(#nodejs_icon_mask_a)\">\n        <path\n          d=\"M18.648 2.717 3.248-4.86-4.646 11.31l15.399 7.58 7.896-16.174z\"\n          fill=\"url(#nodejs_icon_linear_gradient_b)\"\n        />\n      </g>\n      <mask\n        id=\"nodejs_icon_mask_c\"\n        style={{ maskType: 'luminance' }}\n        maskUnits=\"userSpaceOnUse\"\n        x=\"1\"\n        y=\"0\"\n        width=\"12\"\n        height=\"15\"\n      >\n        <path\n          d=\"M1.01 10.571a.66.66 0 0 0 .195.172l4.688 2.718.781.451a.66.66 0 0 0 .51.063l5.764-10.597a.653.653 0 0 0-.153-.122L9.216 1.181 7.325.09a.688.688 0 0 0-.171-.07L1.01 10.572z\"\n          fill=\"#fff\"\n        />\n      </mask>\n      <g mask=\"url(#nodejs_icon_mask_c)\">\n        <path\n          d=\"M-5.647 4.96 5.226 19.736 19.606 9.07 8.734-5.707-5.647 4.96z\"\n          fill=\"url(#nodejs_icon_linear_gradient_d)\"\n        />\n      </g>\n      <g>\n        <mask\n          id=\"nodejs_icon_mask_e\"\n          style={{ maskType: 'luminance' }}\n          maskUnits=\"userSpaceOnUse\"\n          x=\"1\"\n          y=\"0\"\n          width=\"13\"\n          height=\"14\"\n        >\n          <path\n            d=\"M6.935.003a.665.665 0 0 0-.264.085l-5.45 3.158 5.877 10.747a.653.653 0 0 0 .235-.082l5.465-3.17a.665.665 0 0 0 .319-.452L7.127.014a.684.684 0 0 0-.189-.01\"\n            fill=\"#fff\"\n          />\n        </mask>\n        <g mask=\"url(#nodejs_icon_mask_e)\">\n          <path\n            d=\"M1.222.001v13.992h11.893V0H1.222z\"\n            fill=\"url(#nodejs_icon_linear_gradient_f)\"\n          />\n        </g>\n      </g>\n      <defs>\n        <linearGradient\n          id=\"nodejs_icon_linear_gradient_b\"\n          x1=\"10.944\"\n          y1=\"-1.084\"\n          x2=\"2.997\"\n          y2=\"15.062\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\".3\" stopColor=\"#676767\" />\n          <stop offset=\".5\" stopColor=\"#858585\" />\n          <stop offset=\".8\" stopColor=\"#989A98\" />\n        </linearGradient>\n        <linearGradient\n          id=\"nodejs_icon_linear_gradient_d\"\n          x1=\"-.145\"\n          y1=\"12.433\"\n          x2=\"14.277\"\n          y2=\"1.819\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\".57\" stopColor=\"#747474\" />\n          <stop offset=\".72\" stopColor=\"#707070\" />\n          <stop offset=\"1\" stopColor=\"#929292\" />\n        </linearGradient>\n        <linearGradient\n          id=\"nodejs_icon_linear_gradient_f\"\n          x1=\"1.226\"\n          y1=\"6.997\"\n          x2=\"13.117\"\n          y2=\"6.997\"\n          gradientUnits=\"userSpaceOnUse\"\n        >\n          <stop offset=\".16\" stopColor=\"#878787\" />\n          <stop offset=\".38\" stopColor=\"#A9A9A9\" />\n          <stop offset=\".47\" stopColor=\"#A5A5A5\" />\n          <stop offset=\".7\" stopColor=\"#8F8F8F\" />\n          <stop offset=\".9\" stopColor=\"#626262\" />\n        </linearGradient>\n      </defs>\n    </svg>\n  )\n}\n\nconst label =\n  'Learn more about enabling Node.js inspector for server code with Chrome DevTools'\n\nexport function NodejsInspectorButton({\n  devtoolsFrontendUrl,\n}: {\n  devtoolsFrontendUrl: string | undefined\n}) {\n  const content = devtoolsFrontendUrl || ''\n  const disabled = !content || !isChromeBrowser\n  if (disabled) {\n    return (\n      <a\n        title={label}\n        aria-label={label}\n        className=\"nodejs-inspector-button\"\n        href={`https://nextjs.org/docs/app/building-your-application/configuring/debugging#server-side-code`}\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n      >\n        <NodeJsDisabledIcon\n          className=\"error-overlay-toolbar-button-icon\"\n          width={14}\n          height={14}\n        />\n      </a>\n    )\n  }\n  return (\n    <CopyButton\n      data-nextjs-data-runtime-error-copy-devtools-url\n      className=\"nodejs-inspector-button\"\n      actionLabel={'Copy Chrome DevTools URL'}\n      successLabel=\"Copied\"\n      content={content}\n      icon={\n        <NodeJsIcon\n          className=\"error-overlay-toolbar-button-icon\"\n          width={14}\n          height={14}\n        />\n      }\n    />\n  )\n}\n"], "names": ["NodejsInspectorButton", "isChrome", "window", "isChromium", "chrome", "vendorName", "navigator", "vendor", "undefined", "isChrome<PERSON><PERSON><PERSON>", "NodeJsIcon", "props", "svg", "width", "height", "viewBox", "fill", "xmlns", "mask", "id", "style", "maskType", "maskUnits", "x", "y", "path", "d", "g", "defs", "linearGradient", "x1", "y1", "x2", "y2", "gradientUnits", "stop", "offset", "stopColor", "NodeJsDisabledIcon", "label", "devtoolsFrontendUrl", "content", "disabled", "a", "title", "aria-label", "className", "href", "target", "rel", "Copy<PERSON><PERSON><PERSON>", "data-nextjs-data-runtime-error-copy-devtools-url", "actionLabel", "successLabel", "icon"], "mappings": ";;;;+BA6PgBA;;;eAAAA;;;;4BA7PW;AAE3B,+DAA+D;AAC/D,6EAA6E;AAC7E,SAASC;IACP,IAAI,OAAOC,WAAW,aAAa,OAAO;IAC1C,MAAMC,aAAa,YAAYD,UAAUA,OAAOE,MAAM;IACtD,MAAMC,aAAaH,OAAOI,SAAS,CAACC,MAAM;IAE1C,OACEJ,eAAe,QACfA,eAAeK,aACfH,eAAe;AAEnB;AAEA,MAAMI,kBAAkBR;AAExB,SAASS,WAAWC,KAAU;IAC5B,qBACE,sBAACC;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;QACL,GAAGN,KAAK;;0BAET,qBAACO;gBACCC,IAAG;gBACHC,OAAO;oBAAEC,UAAU;gBAAY;gBAC/BC,WAAU;gBACVC,GAAE;gBACFC,GAAE;gBACFX,OAAM;gBACNC,QAAO;0BAEP,cAAA,qBAACW;oBACCC,GAAE;oBACFV,MAAK;;;0BAGT,qBAACW;gBAAET,MAAK;0BACN,cAAA,qBAACO;oBACCC,GAAE;oBACFV,MAAK;;;0BAGT,qBAACE;gBACCC,IAAG;gBACHC,OAAO;oBAAEC,UAAU;gBAAY;gBAC/BC,WAAU;gBACVC,GAAE;gBACFC,GAAE;gBACFX,OAAM;gBACNC,QAAO;0BAEP,cAAA,qBAACW;oBACCC,GAAE;oBACFV,MAAK;;;0BAGT,qBAACW;gBAAET,MAAK;0BACN,cAAA,qBAACO;oBACCC,GAAE;oBACFV,MAAK;;;0BAGT,sBAACW;;kCACC,qBAACT;wBACCC,IAAG;wBACHC,OAAO;4BAAEC,UAAU;wBAAY;wBAC/BC,WAAU;wBACVC,GAAE;wBACFC,GAAE;wBACFX,OAAM;wBACNC,QAAO;kCAEP,cAAA,qBAACW;4BACCC,GAAE;4BACFV,MAAK;;;kCAGT,qBAACW;wBAAET,MAAK;kCACN,cAAA,qBAACO;4BACCC,GAAE;4BACFV,MAAK;;;;;0BAIX,sBAACY;;kCACC,sBAACC;wBACCV,IAAG;wBACHW,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,QAAO;gCAAKC,WAAU;;0CAC5B,qBAACF;gCAAKC,QAAO;gCAAKC,WAAU;;0CAC5B,qBAACF;gCAAKC,QAAO;gCAAKC,WAAU;;;;kCAE9B,sBAACR;wBACCV,IAAG;wBACHW,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAIC,WAAU;;;;kCAE7B,sBAACR;wBACCV,IAAG;wBACHW,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAKC,WAAU;;0CAC5B,qBAACF;gCAAKC,QAAO;gCAAKC,WAAU;;;;;;;;AAKtC;AAEA,SAASC,mBAAmB3B,KAAU;IACpC,qBACE,sBAACC;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;QACL,GAAGN,KAAK;;0BAET,qBAACO;gBACCC,IAAG;gBACHC,OAAO;oBAAEC,UAAU;gBAAY;gBAC/BC,WAAU;gBACVC,GAAE;gBACFC,GAAE;gBACFX,OAAM;gBACNC,QAAO;0BAEP,cAAA,qBAACW;oBACCC,GAAE;oBACFV,MAAK;;;0BAGT,qBAACW;gBAAET,MAAK;0BACN,cAAA,qBAACO;oBACCC,GAAE;oBACFV,MAAK;;;0BAGT,qBAACE;gBACCC,IAAG;gBACHC,OAAO;oBAAEC,UAAU;gBAAY;gBAC/BC,WAAU;gBACVC,GAAE;gBACFC,GAAE;gBACFX,OAAM;gBACNC,QAAO;0BAEP,cAAA,qBAACW;oBACCC,GAAE;oBACFV,MAAK;;;0BAGT,qBAACW;gBAAET,MAAK;0BACN,cAAA,qBAACO;oBACCC,GAAE;oBACFV,MAAK;;;0BAGT,sBAACW;;kCACC,qBAACT;wBACCC,IAAG;wBACHC,OAAO;4BAAEC,UAAU;wBAAY;wBAC/BC,WAAU;wBACVC,GAAE;wBACFC,GAAE;wBACFX,OAAM;wBACNC,QAAO;kCAEP,cAAA,qBAACW;4BACCC,GAAE;4BACFV,MAAK;;;kCAGT,qBAACW;wBAAET,MAAK;kCACN,cAAA,qBAACO;4BACCC,GAAE;4BACFV,MAAK;;;;;0BAIX,sBAACY;;kCACC,sBAACC;wBACCV,IAAG;wBACHW,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,QAAO;gCAAKC,WAAU;;0CAC5B,qBAACF;gCAAKC,QAAO;gCAAKC,WAAU;;0CAC5B,qBAACF;gCAAKC,QAAO;gCAAKC,WAAU;;;;kCAE9B,sBAACR;wBACCV,IAAG;wBACHW,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAIC,WAAU;;;;kCAE7B,sBAACR;wBACCV,IAAG;wBACHW,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,IAAG;wBACHC,eAAc;;0CAEd,qBAACC;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAMC,WAAU;;0CAC7B,qBAACF;gCAAKC,QAAO;gCAAKC,WAAU;;0CAC5B,qBAACF;gCAAKC,QAAO;gCAAKC,WAAU;;;;;;;;AAKtC;AAEA,MAAME,QACJ;AAEK,SAASvC,sBAAsB,KAIrC;IAJqC,IAAA,EACpCwC,mBAAmB,EAGpB,GAJqC;IAKpC,MAAMC,UAAUD,uBAAuB;IACvC,MAAME,WAAW,CAACD,WAAW,CAAChC;IAC9B,IAAIiC,UAAU;QACZ,qBACE,qBAACC;YACCC,OAAOL;YACPM,cAAYN;YACZO,WAAU;YACVC,MAAO;YACPC,QAAO;YACPC,KAAI;sBAEJ,cAAA,qBAACX;gBACCQ,WAAU;gBACVjC,OAAO;gBACPC,QAAQ;;;IAIhB;IACA,qBACE,qBAACoC,sBAAU;QACTC,kDAAgD;QAChDL,WAAU;QACVM,aAAa;QACbC,cAAa;QACbZ,SAASA;QACTa,oBACE,qBAAC5C;YACCoC,WAAU;YACVjC,OAAO;YACPC,QAAQ;;;AAKlB"}