{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/code-frame/parse-code-frame.ts"], "sourcesContent": ["import type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\nimport Anser, { type AnserJsonEntry } from 'next/dist/compiled/anser'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\n\n// Strip leading spaces out of the code frame\nexport function formatCodeFrame(codeFrame: string) {\n  const lines = codeFrame.split(/\\r?\\n/g)\n\n  // Find the minimum length of leading spaces after `|` in the code frame\n  const miniLeadingSpacesLength = lines\n    .map((line) =>\n      /^>? +\\d+ +\\| [ ]+/.exec(stripAnsi(line)) === null\n        ? null\n        : /^>? +\\d+ +\\| ( *)/.exec(stripAnsi(line))\n    )\n    .filter(Boolean)\n    .map((v) => v!.pop()!)\n    .reduce((c, n) => (isNaN(c) ? n.length : Math.min(c, n.length)), NaN)\n\n  // When the minimum length of leading spaces is greater than 1, remove them\n  // from the code frame to help the indentation looks better when there's a lot leading spaces.\n  if (miniLeadingSpacesLength > 1) {\n    return lines\n      .map((line, a) =>\n        ~(a = line.indexOf('|'))\n          ? line.substring(0, a) +\n            line.substring(a).replace(`^\\\\ {${miniLeadingSpacesLength}}`, '')\n          : line\n      )\n      .join('\\n')\n  }\n  return lines.join('\\n')\n}\n\nexport function groupCodeFrameLines(formattedFrame: string) {\n  // Map the decoded lines to a format that can be rendered\n  const decoded = Anser.ansiToJson(formattedFrame, {\n    json: true,\n    use_classes: true,\n    remove_empty: true,\n  })\n  const lines: (typeof decoded)[] = []\n\n  let line: typeof decoded = []\n  for (const token of decoded) {\n    if (token.content === '\\n') {\n      lines.push(line)\n      line = []\n    } else {\n      line.push(token)\n    }\n  }\n  if (line.length > 0) {\n    lines.push(line)\n  }\n\n  return lines\n}\n\nexport function parseLineNumberFromCodeFrameLine(\n  line: AnserJsonEntry[],\n  stackFrame: StackFrame\n) {\n  let lineNumberToken: AnserJsonEntry | undefined\n  let lineNumber: string | undefined\n  // parse line number from line first 2 tokens\n  // e.g. ` > 1 | const foo = 'bar'` => `1`, first token is `1 |`\n  // e.g. `  2 | const foo = 'bar'` => `2`. first 2 tokens are ' ' and ' 2 |'\n  // console.log('line', line)\n  if (line[0]?.content === '>' || line[0]?.content === ' ') {\n    lineNumberToken = line[1]\n    lineNumber = lineNumberToken?.content?.replace('|', '')?.trim()\n  }\n\n  // When the line number is possibly undefined, it can be just the non-source code line\n  // e.g. the ^ sign can also take a line, we skip rendering line number for it\n  return {\n    lineNumber,\n    isErroredLine: lineNumber === stackFrame.lineNumber?.toString(),\n  }\n}\n"], "names": ["formatCodeFrame", "groupCodeFrameLines", "parseLineNumberFromCodeFrameLine", "codeFrame", "lines", "split", "miniLeadingSpacesLength", "map", "line", "exec", "stripAnsi", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "a", "indexOf", "substring", "replace", "join", "formattedFrame", "decoded", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "token", "content", "push", "stackFrame", "lineNumberToken", "lineNumber", "trim", "isErroredLine", "toString"], "mappings": ";;;;;;;;;;;;;;;;IAKgBA,eAAe;eAAfA;;IA6BAC,mBAAmB;eAAnBA;;IAyBAC,gCAAgC;eAAhCA;;;;gEA1D2B;oEACrB;AAGf,SAASF,gBAAgBG,SAAiB;IAC/C,MAAMC,QAAQD,UAAUE,KAAK,CAAC;IAE9B,wEAAwE;IACxE,MAAMC,0BAA0BF,MAC7BG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,WAAW,OAC1C,OACA,oBAAoBC,IAAI,CAACC,IAAAA,kBAAS,EAACF,QAExCG,MAAM,CAACC,SACPL,GAAG,CAAC,CAACM,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;IAEnE,2EAA2E;IAC3E,8FAA8F;IAC9F,IAAIhB,0BAA0B,GAAG;QAC/B,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMe,IACV,CAAEA,CAAAA,IAAIf,KAAKgB,OAAO,CAAC,IAAG,IAClBhB,KAAKiB,SAAS,CAAC,GAAGF,KAClBf,KAAKiB,SAAS,CAACF,GAAGG,OAAO,CAAC,AAAC,UAAOpB,0BAAwB,KAAI,MAC9DE,MAELmB,IAAI,CAAC;IACV;IACA,OAAOvB,MAAMuB,IAAI,CAAC;AACpB;AAEO,SAAS1B,oBAAoB2B,cAAsB;IACxD,yDAAyD;IACzD,MAAMC,UAAUC,cAAK,CAACC,UAAU,CAACH,gBAAgB;QAC/CI,MAAM;QACNC,aAAa;QACbC,cAAc;IAChB;IACA,MAAM9B,QAA4B,EAAE;IAEpC,IAAII,OAAuB,EAAE;IAC7B,KAAK,MAAM2B,SAASN,QAAS;QAC3B,IAAIM,MAAMC,OAAO,KAAK,MAAM;YAC1BhC,MAAMiC,IAAI,CAAC7B;YACXA,OAAO,EAAE;QACX,OAAO;YACLA,KAAK6B,IAAI,CAACF;QACZ;IACF;IACA,IAAI3B,KAAKW,MAAM,GAAG,GAAG;QACnBf,MAAMiC,IAAI,CAAC7B;IACb;IAEA,OAAOJ;AACT;AAEO,SAASF,iCACdM,IAAsB,EACtB8B,UAAsB;QAQlB9B,QAA4BA,SASA8B;IAfhC,IAAIC;IACJ,IAAIC;IACJ,6CAA6C;IAC7C,+DAA+D;IAC/D,2EAA2E;IAC3E,4BAA4B;IAC5B,IAAIhC,EAAAA,SAAAA,IAAI,CAAC,EAAE,qBAAPA,OAAS4B,OAAO,MAAK,OAAO5B,EAAAA,UAAAA,IAAI,CAAC,EAAE,qBAAPA,QAAS4B,OAAO,MAAK,KAAK;YAE3CG,kCAAAA;QADbA,kBAAkB/B,IAAI,CAAC,EAAE;QACzBgC,aAAaD,oCAAAA,2BAAAA,gBAAiBH,OAAO,sBAAxBG,mCAAAA,yBAA0Bb,OAAO,CAAC,KAAK,wBAAvCa,iCAA4CE,IAAI;IAC/D;IAEA,sFAAsF;IACtF,6EAA6E;IAC7E,OAAO;QACLD;QACAE,eAAeF,iBAAeF,yBAAAA,WAAWE,UAAU,qBAArBF,uBAAuBK,QAAQ;IAC/D;AACF"}