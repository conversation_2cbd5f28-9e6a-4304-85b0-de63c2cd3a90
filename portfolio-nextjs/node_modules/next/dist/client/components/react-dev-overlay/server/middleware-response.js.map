{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/server/middleware-response.ts"], "sourcesContent": ["import type { ServerResponse } from 'http'\nimport { inspect } from 'util'\n\nexport const middlewareResponse = {\n  noContent(res: ServerResponse) {\n    res.statusCode = 204\n    res.end('No Content')\n  },\n  badRequest(res: ServerResponse) {\n    res.statusCode = 400\n    res.end('Bad Request')\n  },\n  notFound(res: ServerResponse) {\n    res.statusCode = 404\n    res.end('Not Found')\n  },\n  methodNotAllowed(res: ServerResponse) {\n    res.statusCode = 405\n    res.end('Method Not Allowed')\n  },\n  internalServerError(res: ServerResponse, error?: unknown) {\n    res.statusCode = 500\n    res.setHeader('Content-Type', 'text/plain')\n    res.end(\n      error !== undefined\n        ? inspect(error, { colors: false })\n        : 'Internal Server Error'\n    )\n  },\n  json(res: ServerResponse, data: any) {\n    res\n      .setHeader('Content-Type', 'application/json')\n      .end(Buffer.from(JSON.stringify(data)))\n  },\n  jsonString(res: ServerResponse, data: string) {\n    res.setHeader('Content-Type', 'application/json').end(Buffer.from(data))\n  },\n}\n"], "names": ["middlewareResponse", "noContent", "res", "statusCode", "end", "badRequest", "notFound", "methodNotAllowed", "internalServerError", "error", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "inspect", "colors", "json", "data", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "jsonString"], "mappings": ";;;;+BAGaA;;;eAAAA;;;sBAFW;AAEjB,MAAMA,qBAAqB;IAChCC,WAAUC,GAAmB;QAC3BA,IAAIC,UAAU,GAAG;QACjBD,IAAIE,GAAG,CAAC;IACV;IACAC,YAAWH,GAAmB;QAC5BA,IAAIC,UAAU,GAAG;QACjBD,IAAIE,GAAG,CAAC;IACV;IACAE,UAASJ,GAAmB;QAC1BA,IAAIC,UAAU,GAAG;QACjBD,IAAIE,GAAG,CAAC;IACV;IACAG,kBAAiBL,GAAmB;QAClCA,IAAIC,UAAU,GAAG;QACjBD,IAAIE,GAAG,CAAC;IACV;IACAI,qBAAoBN,GAAmB,EAAEO,KAAe;QACtDP,IAAIC,UAAU,GAAG;QACjBD,IAAIQ,SAAS,CAAC,gBAAgB;QAC9BR,IAAIE,GAAG,CACLK,UAAUE,YACNC,IAAAA,aAAO,EAACH,OAAO;YAAEI,QAAQ;QAAM,KAC/B;IAER;IACAC,MAAKZ,GAAmB,EAAEa,IAAS;QACjCb,IACGQ,SAAS,CAAC,gBAAgB,oBAC1BN,GAAG,CAACY,OAAOC,IAAI,CAACC,KAAKC,SAAS,CAACJ;IACpC;IACAK,YAAWlB,GAAmB,EAAEa,IAAY;QAC1Cb,IAAIQ,SAAS,CAAC,gBAAgB,oBAAoBN,GAAG,CAACY,OAAOC,IAAI,CAACF;IACpE;AACF"}