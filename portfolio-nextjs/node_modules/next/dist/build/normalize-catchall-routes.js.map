{"version": 3, "sources": ["../../src/build/normalize-catchall-routes.ts"], "sourcesContent": ["import { isInterceptionRouteAppPath } from '../shared/lib/router/utils/interception-routes'\nimport { AppPathnameNormalizer } from '../server/normalizers/built/app/app-pathname-normalizer'\n\n/**\n * This function will transform the appPaths in order to support catch-all routes and parallel routes.\n * It will traverse the appPaths, looking for catch-all routes and try to find parallel routes that could match\n * the catch-all. If it finds a match, it will add the catch-all to the parallel route's list of possible routes.\n *\n * @param appPaths The appPaths to transform\n */\nexport function normalizeCatchAllRoutes(\n  appPaths: Record<string, string[]>,\n  normalizer = new AppPathnameNormalizer()\n) {\n  const catchAllRoutes = [\n    ...new Set(\n      Object.values(appPaths)\n        .flat()\n        .filter(isCatchAllRoute)\n        // Sorting is important because we want to match the most specific path.\n        .sort((a, b) => b.split('/').length - a.split('/').length)\n    ),\n  ]\n\n  // interception routes should only be matched by a single entrypoint\n  // we don't want to push a catch-all route to an interception route\n  // because it would mean the interception would be handled by the wrong page component\n  const filteredAppPaths = Object.keys(appPaths).filter(\n    (route) => !isInterceptionRouteAppPath(route)\n  )\n\n  for (const appPath of filteredAppPaths) {\n    for (const catchAllRoute of catchAllRoutes) {\n      const normalizedCatchAllRoute = normalizer.normalize(catchAllRoute)\n      const normalizedCatchAllRouteBasePath = normalizedCatchAllRoute.slice(\n        0,\n        normalizedCatchAllRoute.search(catchAllRouteRegex)\n      )\n\n      if (\n        // check if the appPath could match the catch-all\n        appPath.startsWith(normalizedCatchAllRouteBasePath) &&\n        // check if there's not already a slot value that could match the catch-all\n        !appPaths[appPath].some((path) => hasMatchedSlots(path, catchAllRoute))\n      ) {\n        // optional catch-all routes are not currently supported, but leaving this logic in place\n        // for when they are eventually supported.\n        if (isOptionalCatchAll(catchAllRoute)) {\n          // optional catch-all routes should match both the root segment and any segment after it\n          // for example, `/[[...slug]]` should match `/` and `/foo` and `/foo/bar`\n          appPaths[appPath].push(catchAllRoute)\n        } else if (isCatchAll(catchAllRoute)) {\n          // regular catch-all (single bracket) should only match segments after it\n          // for example, `/[...slug]` should match `/foo` and `/foo/bar` but not `/`\n          if (normalizedCatchAllRouteBasePath !== appPath) {\n            appPaths[appPath].push(catchAllRoute)\n          }\n        }\n      }\n    }\n  }\n}\n\nfunction hasMatchedSlots(path1: string, path2: string): boolean {\n  const slots1 = path1.split('/').filter(isMatchableSlot)\n  const slots2 = path2.split('/').filter(isMatchableSlot)\n\n  // if the catch-all route does not have the same number of slots as the app path, it can't match\n  if (slots1.length !== slots2.length) return false\n\n  // compare the slots in both paths. For there to be a match, each slot must be the same\n  for (let i = 0; i < slots1.length; i++) {\n    if (slots1[i] !== slots2[i]) return false\n  }\n\n  return true\n}\n\n/**\n * Returns true for slots that should be considered when checking for match compatibility.\n * Excludes children slots because these are similar to having a segment-level `page`\n * which would cause a slot length mismatch when comparing it to a catch-all route.\n */\nfunction isMatchableSlot(segment: string): boolean {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nconst catchAllRouteRegex = /\\[?\\[\\.\\.\\./\n\nfunction isCatchAllRoute(pathname: string): boolean {\n  // Optional catch-all slots are not currently supported, and as such they are not considered when checking for match compatability.\n  return !isOptionalCatchAll(pathname) && isCatchAll(pathname)\n}\n\nfunction isOptionalCatchAll(pathname: string): boolean {\n  return pathname.includes('[[...')\n}\n\nfunction isCatchAll(pathname: string): boolean {\n  return pathname.includes('[...')\n}\n"], "names": ["normalizeCatchAllRoutes", "appPaths", "normalizer", "AppPathnameNormalizer", "catchAllRoutes", "Set", "Object", "values", "flat", "filter", "isCatchAllRoute", "sort", "a", "b", "split", "length", "filteredAppPaths", "keys", "route", "isInterceptionRouteAppPath", "appPath", "catchAllRoute", "normalizedCatchAllRoute", "normalize", "normalizedCatchAllRouteBasePath", "slice", "search", "catchAllRouteRegex", "startsWith", "some", "path", "hasMatchedSlots", "isOptionalCatchAll", "push", "isCatchAll", "path1", "path2", "slots1", "isMatchableSlot", "slots2", "i", "segment", "pathname", "includes"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;oCAV2B;uCACL;AAS/B,SAASA,wBACdC,QAAkC,EAClCC,aAAa,IAAIC,4CAAqB,EAAE;IAExC,MAAMC,iBAAiB;WAClB,IAAIC,IACLC,OAAOC,MAAM,CAACN,UACXO,IAAI,GACJC,MAAM,CAACC,gBACR,wEAAwE;SACvEC,IAAI,CAAC,CAACC,GAAGC,IAAMA,EAAEC,KAAK,CAAC,KAAKC,MAAM,GAAGH,EAAEE,KAAK,CAAC,KAAKC,MAAM;KAE9D;IAED,oEAAoE;IACpE,mEAAmE;IACnE,sFAAsF;IACtF,MAAMC,mBAAmBV,OAAOW,IAAI,CAAChB,UAAUQ,MAAM,CACnD,CAACS,QAAU,CAACC,IAAAA,8CAA0B,EAACD;IAGzC,KAAK,MAAME,WAAWJ,iBAAkB;QACtC,KAAK,MAAMK,iBAAiBjB,eAAgB;YAC1C,MAAMkB,0BAA0BpB,WAAWqB,SAAS,CAACF;YACrD,MAAMG,kCAAkCF,wBAAwBG,KAAK,CACnE,GACAH,wBAAwBI,MAAM,CAACC;YAGjC,IACE,iDAAiD;YACjDP,QAAQQ,UAAU,CAACJ,oCACnB,2EAA2E;YAC3E,CAACvB,QAAQ,CAACmB,QAAQ,CAACS,IAAI,CAAC,CAACC,OAASC,gBAAgBD,MAAMT,iBACxD;gBACA,yFAAyF;gBACzF,0CAA0C;gBAC1C,IAAIW,mBAAmBX,gBAAgB;oBACrC,wFAAwF;oBACxF,yEAAyE;oBACzEpB,QAAQ,CAACmB,QAAQ,CAACa,IAAI,CAACZ;gBACzB,OAAO,IAAIa,WAAWb,gBAAgB;oBACpC,yEAAyE;oBACzE,2EAA2E;oBAC3E,IAAIG,oCAAoCJ,SAAS;wBAC/CnB,QAAQ,CAACmB,QAAQ,CAACa,IAAI,CAACZ;oBACzB;gBACF;YACF;QACF;IACF;AACF;AAEA,SAASU,gBAAgBI,KAAa,EAAEC,KAAa;IACnD,MAAMC,SAASF,MAAMrB,KAAK,CAAC,KAAKL,MAAM,CAAC6B;IACvC,MAAMC,SAASH,MAAMtB,KAAK,CAAC,KAAKL,MAAM,CAAC6B;IAEvC,gGAAgG;IAChG,IAAID,OAAOtB,MAAM,KAAKwB,OAAOxB,MAAM,EAAE,OAAO;IAE5C,uFAAuF;IACvF,IAAK,IAAIyB,IAAI,GAAGA,IAAIH,OAAOtB,MAAM,EAAEyB,IAAK;QACtC,IAAIH,MAAM,CAACG,EAAE,KAAKD,MAAM,CAACC,EAAE,EAAE,OAAO;IACtC;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAASF,gBAAgBG,OAAe;IACtC,OAAOA,QAAQb,UAAU,CAAC,QAAQa,YAAY;AAChD;AAEA,MAAMd,qBAAqB;AAE3B,SAASjB,gBAAgBgC,QAAgB;IACvC,mIAAmI;IACnI,OAAO,CAACV,mBAAmBU,aAAaR,WAAWQ;AACrD;AAEA,SAASV,mBAAmBU,QAAgB;IAC1C,OAAOA,SAASC,QAAQ,CAAC;AAC3B;AAEA,SAAST,WAAWQ,QAAgB;IAClC,OAAOA,SAASC,QAAQ,CAAC;AAC3B"}