{"version": 3, "sources": ["../../../src/build/next-config-ts/require-hook.ts"], "sourcesContent": ["import type { Options as SWCOptions } from '@swc/core'\nimport Module from 'node:module'\nimport { readFileSync } from 'node:fs'\nimport { dirname } from 'node:path'\n\nconst oldJSHook = require.extensions['.js']\nconst extensions = ['.ts', '.cts', '.mts', '.cjs', '.mjs']\n\nexport function registerHook(swcOptions: SWCOptions) {\n  // lazy require swc since it loads React before even setting NODE_ENV\n  // resulting loading Development React on Production\n  const { transformSync } = require('../swc')\n\n  require.extensions['.js'] = function (mod: any, oldFilename) {\n    try {\n      return oldJSHook(mod, oldFilename)\n    } catch (error) {\n      if ((error as NodeJS.ErrnoException).code !== 'ERR_REQUIRE_ESM') {\n        throw error\n      }\n\n      // calling oldJSHook throws ERR_REQUIRE_ESM, so run _compile manually\n      // TODO: investigate if we can remove readFileSync\n      const content = readFileSync(oldFilename, 'utf8')\n      const { code } = transformSync(content, swcOptions)\n      mod._compile(code, oldFilename)\n    }\n  }\n\n  for (const ext of extensions) {\n    const oldHook = require.extensions[ext] ?? oldJSHook\n    require.extensions[ext] = function (mod: any, oldFilename) {\n      const _compile = mod._compile\n\n      mod._compile = function (code: string, filename: string) {\n        const swc = transformSync(code, swcOptions)\n        return _compile.call(this, swc.code, filename)\n      }\n\n      return oldHook(mod, oldFilename)\n    }\n  }\n}\n\nexport function deregisterHook() {\n  require.extensions['.js'] = oldJSHook\n  extensions.forEach((ext) => delete require.extensions[ext])\n}\n\nexport function requireFromString(code: string, filename: string) {\n  const paths = (Module as any)._nodeModulePaths(dirname(filename))\n  const m = new Module(filename, module.parent!) as any\n  m.paths = paths\n  m._compile(code, filename)\n  return m.exports\n}\n"], "names": ["deregisterHook", "registerHook", "requireFromString", "oldJSHook", "require", "extensions", "swcOptions", "transformSync", "mod", "oldFilename", "error", "code", "content", "readFileSync", "_compile", "ext", "oldHook", "filename", "swc", "call", "for<PERSON>ach", "paths", "<PERSON><PERSON><PERSON>", "_nodeModulePaths", "dirname", "m", "module", "parent", "exports"], "mappings": ";;;;;;;;;;;;;;;;IA4CgBA,cAAc;eAAdA;;IApCAC,YAAY;eAAZA;;IAyCAC,iBAAiB;eAAjBA;;;mEAhDG;wBACU;0BACL;;;;;;AAExB,MAAMC,YAAYC,QAAQC,UAAU,CAAC,MAAM;AAC3C,MAAMA,aAAa;IAAC;IAAO;IAAQ;IAAQ;IAAQ;CAAO;AAEnD,SAASJ,aAAaK,UAAsB;IACjD,qEAAqE;IACrE,oDAAoD;IACpD,MAAM,EAAEC,aAAa,EAAE,GAAGH,QAAQ;IAElCA,QAAQC,UAAU,CAAC,MAAM,GAAG,SAAUG,GAAQ,EAAEC,WAAW;QACzD,IAAI;YACF,OAAON,UAAUK,KAAKC;QACxB,EAAE,OAAOC,OAAO;YACd,IAAI,AAACA,MAAgCC,IAAI,KAAK,mBAAmB;gBAC/D,MAAMD;YACR;YAEA,qEAAqE;YACrE,kDAAkD;YAClD,MAAME,UAAUC,IAAAA,oBAAY,EAACJ,aAAa;YAC1C,MAAM,EAAEE,IAAI,EAAE,GAAGJ,cAAcK,SAASN;YACxCE,IAAIM,QAAQ,CAACH,MAAMF;QACrB;IACF;IAEA,KAAK,MAAMM,OAAOV,WAAY;QAC5B,MAAMW,UAAUZ,QAAQC,UAAU,CAACU,IAAI,IAAIZ;QAC3CC,QAAQC,UAAU,CAACU,IAAI,GAAG,SAAUP,GAAQ,EAAEC,WAAW;YACvD,MAAMK,WAAWN,IAAIM,QAAQ;YAE7BN,IAAIM,QAAQ,GAAG,SAAUH,IAAY,EAAEM,QAAgB;gBACrD,MAAMC,MAAMX,cAAcI,MAAML;gBAChC,OAAOQ,SAASK,IAAI,CAAC,IAAI,EAAED,IAAIP,IAAI,EAAEM;YACvC;YAEA,OAAOD,QAAQR,KAAKC;QACtB;IACF;AACF;AAEO,SAAST;IACdI,QAAQC,UAAU,CAAC,MAAM,GAAGF;IAC5BE,WAAWe,OAAO,CAAC,CAACL,MAAQ,OAAOX,QAAQC,UAAU,CAACU,IAAI;AAC5D;AAEO,SAASb,kBAAkBS,IAAY,EAAEM,QAAgB;IAC9D,MAAMI,QAAQ,AAACC,mBAAM,CAASC,gBAAgB,CAACC,IAAAA,iBAAO,EAACP;IACvD,MAAMQ,IAAI,IAAIH,mBAAM,CAACL,UAAUS,OAAOC,MAAM;IAC5CF,EAAEJ,KAAK,GAAGA;IACVI,EAAEX,QAAQ,CAACH,MAAMM;IACjB,OAAOQ,EAAEG,OAAO;AAClB"}