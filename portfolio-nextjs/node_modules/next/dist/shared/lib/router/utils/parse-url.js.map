{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/parse-url.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nimport { searchParamsToUrlQuery } from './querystring'\nimport { parseRelativeUrl } from './parse-relative-url'\n\nexport interface ParsedUrl {\n  hash: string\n  hostname?: string | null\n  href: string\n  pathname: string\n  port?: string | null\n  protocol?: string | null\n  query: ParsedUrlQuery\n  search: string\n}\n\nexport function parseUrl(url: string): ParsedUrl {\n  if (url.startsWith('/')) {\n    return parseRelativeUrl(url)\n  }\n\n  const parsedURL = new URL(url)\n  return {\n    hash: parsedURL.hash,\n    hostname: parsedURL.hostname,\n    href: parsedURL.href,\n    pathname: parsedURL.pathname,\n    port: parsedURL.port,\n    protocol: parsedURL.protocol,\n    query: searchParamsToUrlQuery(parsedURL.searchParams),\n    search: parsedURL.search,\n  }\n}\n"], "names": ["parseUrl", "url", "startsWith", "parseRelativeUrl", "parsedURL", "URL", "hash", "hostname", "href", "pathname", "port", "protocol", "query", "searchParamsToUrlQuery", "searchParams", "search"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;6BAduB;kCACN;AAa1B,SAASA,SAASC,GAAW;IAClC,IAAIA,IAAIC,UAAU,CAAC,MAAM;QACvB,OAAOC,IAAAA,kCAAgB,EAACF;IAC1B;IAEA,MAAMG,YAAY,IAAIC,IAAIJ;IAC1B,OAAO;QACLK,MAAMF,UAAUE,IAAI;QACpBC,UAAUH,UAAUG,QAAQ;QAC5BC,MAAMJ,UAAUI,IAAI;QACpBC,UAAUL,UAAUK,QAAQ;QAC5BC,MAAMN,UAAUM,IAAI;QACpBC,UAAUP,UAAUO,QAAQ;QAC5BC,OAAOC,IAAAA,mCAAsB,EAACT,UAAUU,YAAY;QACpDC,QAAQX,UAAUW,MAAM;IAC1B;AACF"}