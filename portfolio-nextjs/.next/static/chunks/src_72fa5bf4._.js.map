{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/lib/constants.ts"], "sourcesContent": ["import { Language, BreakpointConfig, SEOConfig } from './types';\n\n// App Configuration\nexport const APP_CONFIG = {\n  name: 'Portfolio',\n  version: '2.0.0',\n  environment: process.env.NODE_ENV as 'development' | 'staging' | 'production',\n  baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',\n} as const;\n\n// Navigation Items\nexport const NAV_ITEMS = [\n  { id: 'home', label: 'Home', href: '#home' },\n  { id: 'about', label: 'About', href: '#about' },\n  { id: 'projects', label: 'Projects', href: '#projects' },\n  { id: 'experience', label: 'Experience', href: '#experience' },\n  { id: 'contact', label: 'Contact', href: '#contact' },\n] as const;\n\n// Languages for Landing Animation\nexport const LANGUAGES: Language[] = [\n  { code: 'en', name: 'English', greeting: 'Hello' },\n  { code: 'es', name: 'Spanish', greeting: '<PERSON><PERSON>' },\n  { code: 'fr', name: 'French', greeting: 'Bonjour' },\n  { code: 'de', name: 'German', greeting: '<PERSON><PERSON>' },\n  { code: 'it', name: 'Italian', greeting: 'Ciao' },\n  { code: 'pt', name: 'Portuguese', greeting: 'Olá' },\n  { code: 'ru', name: 'Russian', greeting: 'Привет' },\n  { code: 'ja', name: 'Japanese', greeting: 'こんにちは' },\n  { code: 'ko', name: 'Korean', greeting: '안녕하세요' },\n  { code: 'zh', name: 'Chinese', greeting: '你好' },\n  { code: 'ar', name: 'Arabic', greeting: 'مرحبا' },\n  { code: 'hi', name: 'Hindi', greeting: 'नमस्ते' },\n];\n\n// Animation Durations (in milliseconds)\nexport const ANIMATION_DURATIONS = {\n  fast: 150,\n  normal: 250,\n  slow: 400,\n  languageSwitch: 250,\n  pageTransition: 500,\n  scrollIndicator: 1000,\n  particleAnimation: 60000, // 1 minute loop\n} as const;\n\n// Responsive Breakpoints\nexport const BREAKPOINTS: BreakpointConfig = {\n  mobile: 768,\n  tablet: 1024,\n  desktop: 1280,\n  wide: 1536,\n};\n\n// Theme Configuration\nexport const THEME_CONFIG = {\n  defaultTheme: 'light' as const,\n  storageKey: 'portfolio-theme',\n  transitionDuration: '0.3s',\n} as const;\n\n// macOS Interface Configuration\nexport const MACOS_CONFIG = {\n  dock: {\n    height: 80,\n    iconSize: 48,\n    hoverScale: 1.5,\n    animationDuration: 200,\n  },\n  window: {\n    minWidth: 400,\n    minHeight: 300,\n    defaultWidth: 800,\n    defaultHeight: 600,\n    headerHeight: 30,\n    borderRadius: 8,\n  },\n  desktop: {\n    gridSize: 80,\n    iconSize: 64,\n    folderIconSize: 72,\n  },\n} as const;\n\n// macOS Dock Items\nexport const MACOS_DOCK_ITEMS = [\n  {\n    id: 'calculator',\n    name: 'Calculator',\n    icon: '/icons/calculator.png',\n    tooltip: 'Calculator',\n    action: 'calculator',\n  },\n  {\n    id: 'github',\n    name: 'GitHub',\n    icon: '/icons/github.png',\n    tooltip: 'GitHub Profile',\n    action: 'github',\n  },\n  {\n    id: 'linkedin',\n    name: 'LinkedIn',\n    icon: '/icons/linkedin.png',\n    tooltip: 'LinkedIn Profile',\n    action: 'linkedin',\n  },\n] as const;\n\n// Particle Animation Configuration\nexport const PARTICLE_CONFIG = {\n  count: 50,\n  maxSize: 3,\n  minSize: 1,\n  speed: 0.5,\n  opacity: {\n    min: 0.1,\n    max: 0.8,\n  },\n  colors: ['#ffffff', '#f0f0f0', '#e0e0e0'],\n} as const;\n\n// Form Configuration\nexport const FORM_CONFIG = {\n  validation: {\n    email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n    phone: /^[\\+]?[1-9][\\d]{0,15}$/,\n    minNameLength: 2,\n    maxNameLength: 50,\n    minMessageLength: 10,\n    maxMessageLength: 1000,\n  },\n  placeholders: {\n    name: 'Your Name',\n    email: '<EMAIL>',\n    subject: 'Subject',\n    message: 'Your message...',\n  },\n} as const;\n\n// SEO Configuration\nexport const SEO_CONFIG: SEOConfig = {\n  title: 'Portfolio - Full Stack Developer',\n  description: 'Professional portfolio showcasing full-stack development projects, skills, and experience.',\n  keywords: [\n    'full-stack developer',\n    'web development',\n    'react',\n    'next.js',\n    'typescript',\n    'portfolio',\n    'software engineer',\n  ],\n  author: 'Your Name',\n  siteUrl: APP_CONFIG.baseUrl,\n  image: '/assets/images/og-image.jpg',\n  twitterHandle: '@yourhandle',\n};\n\n// Social Media Links\nexport const SOCIAL_LINKS = [\n  {\n    platform: 'GitHub',\n    url: 'https://github.com/yourusername',\n    icon: 'Github',\n    label: 'GitHub Profile',\n  },\n  {\n    platform: 'LinkedIn',\n    url: 'https://linkedin.com/in/yourusername',\n    icon: 'Linkedin',\n    label: 'LinkedIn Profile',\n  },\n  {\n    platform: 'Twitter',\n    url: 'https://twitter.com/yourusername',\n    icon: 'Twitter',\n    label: 'Twitter Profile',\n  },\n  {\n    platform: 'Email',\n    url: 'mailto:<EMAIL>',\n    icon: 'Mail',\n    label: 'Send Email',\n  },\n] as const;\n\n// Scroll Configuration\nexport const SCROLL_CONFIG = {\n  smoothScrollDuration: 800,\n  scrollOffset: 80, // Offset for fixed navbar\n  scrollThreshold: 100, // Threshold for scroll direction detection\n  indicatorFadeDistance: 200, // Distance to fade scroll indicator\n} as const;\n\n// Performance Configuration\nexport const PERFORMANCE_CONFIG = {\n  imageOptimization: {\n    quality: 85,\n    formats: ['webp', 'jpg'],\n    sizes: [640, 768, 1024, 1280, 1536],\n  },\n  lazyLoading: {\n    rootMargin: '50px',\n    threshold: 0.1,\n  },\n  debounceDelay: 300,\n  throttleDelay: 100,\n} as const;\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  generic: 'Something went wrong. Please try again.',\n  network: 'Network error. Please check your connection.',\n  validation: {\n    required: 'This field is required.',\n    email: 'Please enter a valid email address.',\n    minLength: (min: number) => `Must be at least ${min} characters long.`,\n    maxLength: (max: number) => `Must be no more than ${max} characters long.`,\n  },\n  form: {\n    submitError: 'Failed to send message. Please try again.',\n    submitSuccess: 'Message sent successfully!',\n  },\n} as const;\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  form: {\n    submitted: 'Thank you for your message! I\\'ll get back to you soon.',\n  },\n  theme: {\n    switched: 'Theme updated successfully.',\n  },\n} as const;\n\n// File Paths\nexport const FILE_PATHS = {\n  resume: '/assets/resume/resume.pdf',\n  images: {\n    profile: '/assets/images/profile.jpg',\n    profileHover: '/assets/images/profile-hover.jpg',\n    ogImage: '/assets/images/og-image.jpg',\n    favicon: '/favicon.ico',\n  },\n  icons: {\n    logo: '/icons/logo.svg',\n    logoLight: '/icons/logo-light.svg',\n    logoDark: '/icons/logo-dark.svg',\n  },\n} as const;\n\n// API Endpoints (if needed)\nexport const API_ENDPOINTS = {\n  contact: '/api/contact',\n  analytics: '/api/analytics',\n} as const;\n\n// Feature Flags\nexport const FEATURES = {\n  analytics: process.env.NODE_ENV === 'production',\n  errorReporting: process.env.NODE_ENV === 'production',\n  performanceMonitoring: process.env.NODE_ENV === 'production',\n  darkMode: true,\n  animations: true,\n  particleSystem: true,\n  macosInterface: true,\n} as const;\n\n// Z-Index Layers\nexport const Z_INDEX = {\n  base: 0,\n  dropdown: 10,\n  sticky: 20,\n  navbar: 30,\n  modal: 40,\n  tooltip: 50,\n  notification: 60,\n  overlay: 70,\n} as const;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAMe;AAHR,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,WAAW;IACX,SAAS,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;AAC/C;AAGO,MAAM,YAAY;IACvB;QAAE,IAAI;QAAQ,OAAO;QAAQ,MAAM;IAAQ;IAC3C;QAAE,IAAI;QAAS,OAAO;QAAS,MAAM;IAAS;IAC9C;QAAE,IAAI;QAAY,OAAO;QAAY,MAAM;IAAY;IACvD;QAAE,IAAI;QAAc,OAAO;QAAc,MAAM;IAAc;IAC7D;QAAE,IAAI;QAAW,OAAO;QAAW,MAAM;IAAW;CACrD;AAGM,MAAM,YAAwB;IACnC;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;IAAQ;IACjD;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;IAAO;IAChD;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;IAAU;IAClD;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;IAAQ;IAChD;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;IAAO;IAChD;QAAE,MAAM;QAAM,MAAM;QAAc,UAAU;IAAM;IAClD;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;IAAS;IAClD;QAAE,MAAM;QAAM,MAAM;QAAY,UAAU;IAAQ;IAClD;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;IAAQ;IAChD;QAAE,MAAM;QAAM,MAAM;QAAW,UAAU;IAAK;IAC9C;QAAE,MAAM;QAAM,MAAM;QAAU,UAAU;IAAQ;IAChD;QAAE,MAAM;QAAM,MAAM;QAAS,UAAU;IAAS;CACjD;AAGM,MAAM,sBAAsB;IACjC,MAAM;IACN,QAAQ;IACR,MAAM;IACN,gBAAgB;IAChB,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;AACrB;AAGO,MAAM,cAAgC;IAC3C,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,MAAM;AACR;AAGO,MAAM,eAAe;IAC1B,cAAc;IACd,YAAY;IACZ,oBAAoB;AACtB;AAGO,MAAM,eAAe;IAC1B,MAAM;QACJ,QAAQ;QACR,UAAU;QACV,YAAY;QACZ,mBAAmB;IACrB;IACA,QAAQ;QACN,UAAU;QACV,WAAW;QACX,cAAc;QACd,eAAe;QACf,cAAc;QACd,cAAc;IAChB;IACA,SAAS;QACP,UAAU;QACV,UAAU;QACV,gBAAgB;IAClB;AACF;AAGO,MAAM,mBAAmB;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;CACD;AAGM,MAAM,kBAAkB;IAC7B,OAAO;IACP,SAAS;IACT,SAAS;IACT,OAAO;IACP,SAAS;QACP,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QAAC;QAAW;QAAW;KAAU;AAC3C;AAGO,MAAM,cAAc;IACzB,YAAY;QACV,OAAO;QACP,OAAO;QACP,eAAe;QACf,eAAe;QACf,kBAAkB;QAClB,kBAAkB;IACpB;IACA,cAAc;QACZ,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;AACF;AAGO,MAAM,aAAwB;IACnC,OAAO;IACP,aAAa;IACb,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,QAAQ;IACR,SAAS,WAAW,OAAO;IAC3B,OAAO;IACP,eAAe;AACjB;AAGO,MAAM,eAAe;IAC1B;QACE,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;IACT;IACA;QACE,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;IACT;IACA;QACE,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;IACT;IACA;QACE,UAAU;QACV,KAAK;QACL,MAAM;QACN,OAAO;IACT;CACD;AAGM,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,cAAc;IACd,iBAAiB;IACjB,uBAAuB;AACzB;AAGO,MAAM,qBAAqB;IAChC,mBAAmB;QACjB,SAAS;QACT,SAAS;YAAC;YAAQ;SAAM;QACxB,OAAO;YAAC;YAAK;YAAK;YAAM;YAAM;SAAK;IACrC;IACA,aAAa;QACX,YAAY;QACZ,WAAW;IACb;IACA,eAAe;IACf,eAAe;AACjB;AAGO,MAAM,iBAAiB;IAC5B,SAAS;IACT,SAAS;IACT,YAAY;QACV,UAAU;QACV,OAAO;QACP,WAAW,CAAC,MAAgB,CAAC,iBAAiB,EAAE,IAAI,iBAAiB,CAAC;QACtE,WAAW,CAAC,MAAgB,CAAC,qBAAqB,EAAE,IAAI,iBAAiB,CAAC;IAC5E;IACA,MAAM;QACJ,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,WAAW;IACb;IACA,OAAO;QACL,UAAU;IACZ;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ;IACR,QAAQ;QACN,SAAS;QACT,cAAc;QACd,SAAS;QACT,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,WAAW;QACX,UAAU;IACZ;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;IACT,WAAW;AACb;AAGO,MAAM,WAAW;IACtB,WAAW,oDAAyB;IACpC,gBAAgB,oDAAyB;IACzC,uBAAuB,oDAAyB;IAChD,UAAU;IACV,YAAY;IACZ,gBAAgB;IAChB,gBAAgB;AAClB;AAGO,MAAM,UAAU;IACrB,MAAM;IACN,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,SAAS;IACT,cAAc;IACd,SAAS;AACX", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/contexts/theme-context.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { Theme, ThemeContextType } from '@/lib/types';\nimport { THEME_CONFIG } from '@/lib/constants';\n\nexport const ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\ninterface ThemeProviderProps {\n  children: React.ReactNode;\n  defaultTheme?: Theme;\n}\n\nexport function ThemeProvider({\n  children,\n  defaultTheme = THEME_CONFIG.defaultTheme\n}: ThemeProviderProps) {\n  const [theme, setTheme] = useState<Theme>(defaultTheme);\n  const [mounted, setMounted] = useState(false);\n\n  // Load theme from localStorage on mount\n  useEffect(() => {\n    const savedTheme = localStorage.getItem(THEME_CONFIG.storageKey) as Theme;\n    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {\n      setTheme(savedTheme);\n    } else {\n      // Check system preference\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches\n        ? 'dark'\n        : 'light';\n      setTheme(systemTheme);\n    }\n    setMounted(true);\n  }, []);\n\n  // Apply theme to document\n  useEffect(() => {\n    if (!mounted) return;\n\n    const root = document.documentElement;\n\n    // Remove previous theme classes\n    root.classList.remove('light', 'dark');\n\n    // Add current theme class\n    root.classList.add(theme);\n\n    // Save to localStorage\n    localStorage.setItem(THEME_CONFIG.storageKey, theme);\n\n    // Update body class for compatibility with existing CSS\n    document.body.classList.toggle('dark-theme', theme === 'dark');\n  }, [theme, mounted]);\n\n  const toggleTheme = () => {\n    setTheme(prev => prev === 'light' ? 'dark' : 'light');\n  };\n\n  const contextValue: ThemeContextType = {\n    theme,\n    toggleTheme,\n    setTheme,\n  };\n\n  // Prevent hydration mismatch by not rendering until mounted\n  if (!mounted) {\n    return (\n      <div className=\"min-h-screen bg-background text-foreground\">\n        {children}\n      </div>\n    );\n  }\n\n  return (\n    <ThemeContext.Provider value={contextValue}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;;;AAJA;;;AAMO,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAOjE,SAAS,cAAc,EAC5B,QAAQ,EACR,eAAe,0HAAA,CAAA,eAAY,CAAC,YAAY,EACrB;;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,aAAa,OAAO,CAAC,0HAAA,CAAA,eAAY,CAAC,UAAU;YAC/D,IAAI,cAAc,CAAC,eAAe,WAAW,eAAe,MAAM,GAAG;gBACnE,SAAS;YACX,OAAO;gBACL,0BAA0B;gBAC1B,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO,GACzE,SACA;gBACJ,SAAS;YACX;YACA,WAAW;QACb;kCAAG,EAAE;IAEL,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,SAAS;YAEd,MAAM,OAAO,SAAS,eAAe;YAErC,gCAAgC;YAChC,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;YAE/B,0BAA0B;YAC1B,KAAK,SAAS,CAAC,GAAG,CAAC;YAEnB,uBAAuB;YACvB,aAAa,OAAO,CAAC,0HAAA,CAAA,eAAY,CAAC,UAAU,EAAE;YAE9C,wDAAwD;YACxD,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,cAAc,UAAU;QACzD;kCAAG;QAAC;QAAO;KAAQ;IAEnB,MAAM,cAAc;QAClB,SAAS,CAAA,OAAQ,SAAS,UAAU,SAAS;IAC/C;IAEA,MAAM,eAAiC;QACrC;QACA;QACA;IACF;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACZ;;;;;;IAGP;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;GAjEgB;KAAA;AAmET,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/interactive/theme-toggle.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ThemeContext } from '@/contexts/theme-context';\nimport { cn } from '@/lib/utils';\n\ninterface ThemeToggleProps {\n  className?: string;\n}\n\nexport function ThemeToggle({ className }: ThemeToggleProps) {\n  const themeContext = useContext(ThemeContext);\n\n  // Safely handle theme context\n  const theme = themeContext?.theme || 'light';\n  const toggleTheme = themeContext?.toggleTheme || (() => {});\n  const isDark = theme === 'dark';\n\n  return (\n    <div className={cn('switch relative', className)}>\n      <input\n        type=\"checkbox\"\n        id=\"toggle\"\n        checked={isDark}\n        onChange={toggleTheme}\n        className=\"absolute left-0 top-0 h-full w-full opacity-0 z-[100] cursor-pointer\"\n      />\n      <label\n        htmlFor=\"toggle\"\n        className=\"block h-[60px] w-[60px] bg-white rounded-full shadow-[inset_0_0_20px_rgba(0,0,0,0.2),inset_0_0_5px_-2px_rgba(0,0,0,0.4)] dark:bg-[var(--dark-theme-background)]\"\n      >\n        <motion.i\n          className={cn(\n            'bulb block relative h-[50px] w-[50px] rounded-full top-[5px] left-[5px] transition-all duration-[0.9s]',\n            isDark\n              ? 'bg-[#a7694a] shadow-[inset_0_0_1px_3px_#a56758,inset_0_0_6px_8px_#6b454f,0_20px_30px_-10px_rgba(0,0,0,0.4),0_0_30px_50px_rgba(253,184,67,0.1)]'\n              : 'bg-[#2d2e32] shadow-[inset_0_0_1px_3px_#2d2e32,inset_0_0_6px_8px_#1e1e20,0_20px_30px_-10px_rgba(0,0,0,0.2)]'\n          )}\n          animate={{\n            backgroundColor: isDark ? '#a7694a' : '#2d2e32',\n          }}\n          transition={{ duration: 0.9 }}\n        >\n          {/* Bulb Center */}\n          <motion.span\n            className={cn(\n              'bulb-center absolute block h-[36px] w-[36px] rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-[0.7s]',\n              isDark\n                ? 'bg-[#feed6b] shadow-[inset_0_0_0_4px_#fdec6a,0_0_12px_10px_#bca83c,0_0_20px_14px_#a1664a]'\n                : 'bg-[#3a3a3c] shadow-[inset_0_0_0_4px_#444]'\n            )}\n            animate={{\n              backgroundColor: isDark ? '#feed6b' : '#3a3a3c',\n            }}\n            transition={{ duration: 0.7 }}\n          >\n            {/* Center highlight */}\n            <motion.span\n              className={cn(\n                'absolute block h-[20px] w-[20px] rounded-full top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-[0.7s]',\n                isDark\n                  ? 'bg-[#fef401] shadow-[0_0_2px_4px_#fdb843]'\n                  : 'bg-[#464648] shadow-[inset_0_0_0_2px_#2a2a2c]'\n              )}\n              animate={{\n                backgroundColor: isDark ? '#fef401' : '#464648',\n              }}\n              transition={{ duration: 0.7 }}\n            />\n          </motion.span>\n\n          {/* Filament 1 */}\n          <motion.span\n            className={cn(\n              'filament-1 absolute block h-[20px] w-[20px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-[0.7s]',\n              'before:content-[\"\"] before:absolute before:block before:h-[10px] before:w-[2px] before:left-[15px] before:transform before:rotate-[10deg]',\n              isDark\n                ? 'before:bg-[#fdb843] before:shadow-[0_0_4px_2px_#fdb843]'\n                : 'before:bg-[#2a2a2c]'\n            )}\n            animate={{\n              opacity: isDark ? 1 : 0.6,\n            }}\n            transition={{ duration: 0.7 }}\n          />\n\n          {/* Filament 2 */}\n          <motion.span\n            className={cn(\n              'filament-2 absolute block h-[20px] w-[20px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rotate-45 transition-all duration-[0.7s]',\n              'before:content-[\"\"] before:absolute before:block before:h-[10px] before:w-[2px] before:left-[15px] before:transform before:rotate-[10deg]',\n              isDark\n                ? 'before:bg-[#fdb843] before:shadow-[0_0_4px_2px_#fdb843]'\n                : 'before:bg-[#2a2a2c]'\n            )}\n            animate={{\n              opacity: isDark ? 1 : 0.6,\n            }}\n            transition={{ duration: 0.7 }}\n          />\n\n          {/* Reflections */}\n          <motion.span\n            className={cn(\n              'reflections absolute block h-[12px] w-[12px] top-[8px] left-[8px] rounded-full transition-all duration-[0.7s]',\n              isDark\n                ? 'bg-[rgba(255,255,255,0.2)]'\n                : 'bg-[rgba(255,255,255,0.1)]'\n            )}\n            animate={{\n              opacity: isDark ? 1 : 0.8,\n            }}\n            transition={{ duration: 0.7 }}\n          >\n            <motion.span\n              className={cn(\n                'absolute block h-[6px] w-[6px] top-[2px] left-[2px] rounded-full transition-all duration-[0.7s]',\n                isDark\n                  ? 'bg-[rgba(255,255,255,0.4)]'\n                  : 'bg-[rgba(255,255,255,0.2)]'\n              )}\n              animate={{\n                opacity: isDark ? 1 : 0.6,\n              }}\n              transition={{ duration: 0.7 }}\n            />\n          </motion.span>\n\n          {/* Sparks */}\n          <motion.span\n            className=\"sparks absolute block h-[50px] w-[50px] top-0 left-0\"\n            animate={{\n              opacity: isDark ? 1 : 0,\n            }}\n            transition={{ duration: 0.7 }}\n          >\n            {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (\n              <motion.i\n                key={i}\n                className={cn(\n                  `spark${i} absolute block h-[4px] w-[4px] rounded-full`,\n                  isDark ? 'bg-[#fdb843]' : 'bg-transparent'\n                )}\n                style={{\n                  top: i === 1 ? '18px' : i === 2 ? '14px' : i === 3 ? '18px' : i === 4 ? '28px' : i === 5 ? '32px' : i === 6 ? '28px' : i === 7 ? '18px' : '14px',\n                  left: i === 1 ? '44px' : i === 2 ? '34px' : i === 3 ? '6px' : i === 4 ? '2px' : i === 5 ? '6px' : i === 6 ? '44px' : i === 7 ? '46px' : '34px',\n                  animationDelay: `${i * 0.1}s`,\n                }}\n                animate={{\n                  scale: isDark ? [1, 1.5, 1] : [0, 0, 0],\n                  opacity: isDark ? [0.8, 1, 0.8] : [0, 0, 0],\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: 'easeInOut',\n                }}\n              />\n            ))}\n          </motion.span>\n        </motion.i>\n      </label>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,YAAY,EAAE,SAAS,EAAoB;;IACzD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,uIAAA,CAAA,eAAY;IAE5C,8BAA8B;IAC9B,MAAM,QAAQ,cAAc,SAAS;IACrC,MAAM,cAAc,cAAc,eAAe,CAAC,KAAO,CAAC;IAC1D,MAAM,SAAS,UAAU;IAEzB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;;0BACpC,6LAAC;gBACC,MAAK;gBACL,IAAG;gBACH,SAAS;gBACT,UAAU;gBACV,WAAU;;;;;;0BAEZ,6LAAC;gBACC,SAAQ;gBACR,WAAU;0BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA,SACI,mJACA;oBAEN,SAAS;wBACP,iBAAiB,SAAS,YAAY;oBACxC;oBACA,YAAY;wBAAE,UAAU;oBAAI;;sCAG5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yJACA,SACI,8FACA;4BAEN,SAAS;gCACP,iBAAiB,SAAS,YAAY;4BACxC;4BACA,YAAY;gCAAE,UAAU;4BAAI;sCAG5B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gCACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6IACA,SACI,8CACA;gCAEN,SAAS;oCACP,iBAAiB,SAAS,YAAY;gCACxC;gCACA,YAAY;oCAAE,UAAU;gCAAI;;;;;;;;;;;sCAKhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2IACA,6IACA,SACI,4DACA;4BAEN,SAAS;gCACP,SAAS,SAAS,IAAI;4BACxB;4BACA,YAAY;gCAAE,UAAU;4BAAI;;;;;;sCAI9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qJACA,6IACA,SACI,4DACA;4BAEN,SAAS;gCACP,SAAS,SAAS,IAAI;4BACxB;4BACA,YAAY;gCAAE,UAAU;4BAAI;;;;;;sCAI9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iHACA,SACI,+BACA;4BAEN,SAAS;gCACP,SAAS,SAAS,IAAI;4BACxB;4BACA,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gCACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mGACA,SACI,+BACA;gCAEN,SAAS;oCACP,SAAS,SAAS,IAAI;gCACxB;gCACA,YAAY;oCAAE,UAAU;gCAAI;;;;;;;;;;;sCAKhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAU;4BACV,SAAS;gCACP,SAAS,SAAS,IAAI;4BACxB;4BACA,YAAY;gCAAE,UAAU;4BAAI;sCAE3B;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAC,KAAK,EAAE,EAAE,4CAA4C,CAAC,EACvD,SAAS,iBAAiB;oCAE5B,OAAO;wCACL,KAAK,MAAM,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM,IAAI,SAAS;wCAC1I,MAAM,MAAM,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,IAAI,SAAS,MAAM,IAAI,SAAS;wCACxI,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;oCAC/B;oCACA,SAAS;wCACP,OAAO,SAAS;4CAAC;4CAAG;4CAAK;yCAAE,GAAG;4CAAC;4CAAG;4CAAG;yCAAE;wCACvC,SAAS,SAAS;4CAAC;4CAAK;4CAAG;yCAAI,GAAG;4CAAC;4CAAG;4CAAG;yCAAE;oCAC7C;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;mCAlBK;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BrB;GA1JgB;KAAA", "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/common/animated-logo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext } from 'react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport { ThemeContext } from '@/contexts/theme-context';\n\ninterface AnimatedLogoProps {\n  className?: string;\n}\n\nexport function AnimatedLogo({ className }: AnimatedLogoProps) {\n  const themeContext = useContext(ThemeContext);\n  const theme = themeContext?.theme || 'light';\n  const isDark = theme === 'dark';\n\n  // Dynamic stroke color based on theme - exactly as in original\n  const strokeColor = isDark ? '#fff' : '#000';\n\n  // Dynamic animation values based on theme - exactly as in original\n  const topBoxAnimateValues = isDark\n    ? 'rgba(255,255,255,1); rgba(100,100,100,0)'\n    : 'rgba(0,0,0,1); rgba(100,100,100,0)';\n\n  const bottomBoxAnimateValues = isDark\n    ? 'rgba(100,100,100,0); rgba(255,255,255,1)'\n    : 'rgba(100,100,100,0); rgba(0,0,0,1)';\n\n  return (\n    <motion.svg\n      viewBox=\"0 0 100 100\"\n      className={cn('logo-svg', className)}\n      style={{ stroke: strokeColor }}\n      initial={{ opacity: 0, scale: 0.8 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.5 }}\n    >\n        <g\n          fill=\"none\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n          strokeWidth=\"6\"\n        >\n          {/* Left line */}\n          <path d=\"M 21 40 V 59\">\n            <animateTransform\n              attributeName=\"transform\"\n              attributeType=\"XML\"\n              type=\"rotate\"\n              values=\"0 21 59; 180 21 59\"\n              dur=\"2s\"\n              repeatCount=\"indefinite\"\n            />\n          </path>\n\n          {/* Right line */}\n          <path d=\"M 79 40 V 59\">\n            <animateTransform\n              attributeName=\"transform\"\n              attributeType=\"XML\"\n              type=\"rotate\"\n              values=\"0 79 59; -180 79 59\"\n              dur=\"2s\"\n              repeatCount=\"indefinite\"\n            />\n          </path>\n\n          {/* Top line */}\n          <path d=\"M 50 21 V 40\">\n            <animate\n              attributeName=\"d\"\n              values=\"M 50 21 V 40; M 50 59 V 40\"\n              dur=\"2s\"\n              repeatCount=\"indefinite\"\n            />\n          </path>\n\n          {/* Bottom line */}\n          <path d=\"M 50 60 V 79\">\n            <animate\n              attributeName=\"d\"\n              values=\"M 50 60 V 79; M 50 98 V 79\"\n              dur=\"2s\"\n              repeatCount=\"indefinite\"\n            />\n          </path>\n\n          {/* Top box */}\n          <path d=\"M 50 21 L 79 40 L 50 60 L 21 40 Z\">\n            <animate\n              attributeName=\"stroke\"\n              values={topBoxAnimateValues}\n              dur=\"2s\"\n              repeatCount=\"indefinite\"\n            />\n          </path>\n\n          {/* Middle box */}\n          <path d=\"M 50 40 L 79 59 L 50 79 L 21 59 Z\" />\n\n          {/* Bottom box */}\n          <path d=\"M 50 59 L 79 78 L 50 98 L 21 78 Z\">\n            <animate\n              attributeName=\"stroke\"\n              values={bottomBoxAnimateValues}\n              dur=\"2s\"\n              repeatCount=\"indefinite\"\n            />\n          </path>\n\n          {/* Main group animation */}\n          <animateTransform\n            attributeName=\"transform\"\n            attributeType=\"XML\"\n            type=\"translate\"\n            values=\"0 0; 0 -19\"\n            dur=\"2s\"\n            repeatCount=\"indefinite\"\n          />\n        </g>\n    </motion.svg>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,aAAa,EAAE,SAAS,EAAqB;;IAC3D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,uIAAA,CAAA,eAAY;IAC5C,MAAM,QAAQ,cAAc,SAAS;IACrC,MAAM,SAAS,UAAU;IAEzB,+DAA+D;IAC/D,MAAM,cAAc,SAAS,SAAS;IAEtC,mEAAmE;IACnE,MAAM,sBAAsB,SACxB,6CACA;IAEJ,MAAM,yBAAyB,SAC3B,6CACA;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAQ;QACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,OAAO;YAAE,QAAQ;QAAY;QAC7B,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;kBAE1B,cAAA,6LAAC;YACC,MAAK;YACL,eAAc;YACd,gBAAe;YACf,aAAY;;8BAGZ,6LAAC;oBAAK,GAAE;8BACN,cAAA,6LAAC;wBACC,eAAc;wBACd,eAAc;wBACd,MAAK;wBACL,QAAO;wBACP,KAAI;wBACJ,aAAY;;;;;;;;;;;8BAKhB,6LAAC;oBAAK,GAAE;8BACN,cAAA,6LAAC;wBACC,eAAc;wBACd,eAAc;wBACd,MAAK;wBACL,QAAO;wBACP,KAAI;wBACJ,aAAY;;;;;;;;;;;8BAKhB,6LAAC;oBAAK,GAAE;8BACN,cAAA,6LAAC;wBACC,eAAc;wBACd,QAAO;wBACP,KAAI;wBACJ,aAAY;;;;;;;;;;;8BAKhB,6LAAC;oBAAK,GAAE;8BACN,cAAA,6LAAC;wBACC,eAAc;wBACd,QAAO;wBACP,KAAI;wBACJ,aAAY;;;;;;;;;;;8BAKhB,6LAAC;oBAAK,GAAE;8BACN,cAAA,6LAAC;wBACC,eAAc;wBACd,QAAQ;wBACR,KAAI;wBACJ,aAAY;;;;;;;;;;;8BAKhB,6LAAC;oBAAK,GAAE;;;;;;8BAGR,6LAAC;oBAAK,GAAE;8BACN,cAAA,6LAAC;wBACC,eAAc;wBACd,QAAQ;wBACR,KAAI;wBACJ,aAAY;;;;;;;;;;;8BAKhB,6LAAC;oBACC,eAAc;oBACd,eAAc;oBACd,MAAK;oBACL,QAAO;oBACP,KAAI;oBACJ,aAAY;;;;;;;;;;;;;;;;;AAKxB;GA/GgB;KAAA", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/layout/navbar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Menu, X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { NAV_ITEMS } from '@/lib/constants';\nimport { ThemeToggle } from '@/components/interactive/theme-toggle';\nimport { AnimatedLogo } from '@/components/common/animated-logo';\n\ninterface NavbarProps {\n  className?: string;\n}\n\nexport function Navbar({ className }: NavbarProps) {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [activeSection, setActiveSection] = useState('home');\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Handle active section detection\n  useEffect(() => {\n    const handleScroll = () => {\n      const sections = NAV_ITEMS.map(item => item.id);\n      const scrollPosition = window.scrollY + 100;\n\n      for (const section of sections) {\n        const element = document.getElementById(section);\n        if (element) {\n          const { offsetTop, offsetHeight } = element;\n          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n            setActiveSection(section);\n            break;\n          }\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleNavClick = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      const offsetTop = element.offsetTop - 80; // Account for navbar height\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n    setIsMobileMenuOpen(false);\n  };\n\n  const toggleMobileMenu = () => {\n    setIsMobileMenuOpen(!isMobileMenuOpen);\n  };\n\n  return (\n    <motion.header\n      className={cn(\n        'fixed top-0 left-0 w-full z-40 transition-all duration-300',\n        isScrolled \n          ? 'bg-background/50 backdrop-blur-md shadow-sm border-b border-border/50' \n          : 'bg-transparent',\n        className\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <motion.div \n            className=\"flex items-center space-x-3\"\n            whileHover={{ scale: 1.05 }}\n            transition={{ duration: 0.2 }}\n          >\n            <AnimatedLogo className=\"w-8 h-8 lg:w-10 lg:h-10\" />\n            <span className=\"text-lg lg:text-xl font-semibold text-foreground\">\n              Narendra Chowdary\n            </span>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-1\">\n            {NAV_ITEMS.map((item) => (\n              <motion.button\n                key={item.id}\n                onClick={() => handleNavClick(item.id)}\n                className={cn(\n                  'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',\n                  'hover:bg-muted/50 hover:text-foreground',\n                  activeSection === item.id\n                    ? 'text-foreground bg-muted/30'\n                    : 'text-muted-foreground'\n                )}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                {item.label}\n              </motion.button>\n            ))}\n          </nav>\n\n          {/* Actions */}\n          <div className=\"flex items-center space-x-3\">\n            {/* Theme Toggle */}\n            <ThemeToggle />\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              onClick={toggleMobileMenu}\n              className=\"md:hidden p-2 rounded-lg bg-muted/20 hover:bg-muted/30 transition-colors\"\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              aria-label=\"Toggle mobile menu\"\n            >\n              <AnimatePresence mode=\"wait\">\n                {isMobileMenuOpen ? (\n                  <motion.div\n                    key=\"close\"\n                    initial={{ rotate: -90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: 90, opacity: 0 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <X className=\"w-5 h-5\" />\n                  </motion.div>\n                ) : (\n                  <motion.div\n                    key=\"menu\"\n                    initial={{ rotate: 90, opacity: 0 }}\n                    animate={{ rotate: 0, opacity: 1 }}\n                    exit={{ rotate: -90, opacity: 0 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <Menu className=\"w-5 h-5\" />\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </motion.button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu Overlay */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            className=\"md:hidden absolute top-full left-0 w-full bg-background/95 backdrop-blur-md border-b border-border/50\"\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <nav className=\"px-4 py-4 space-y-2\">\n              {NAV_ITEMS.map((item, index) => (\n                <motion.button\n                  key={item.id}\n                  onClick={() => handleNavClick(item.id)}\n                  className={cn(\n                    'w-full text-left px-4 py-3 rounded-lg text-base font-medium transition-all duration-200',\n                    'hover:bg-muted/50 hover:text-foreground',\n                    activeSection === item.id\n                      ? 'text-foreground bg-muted/30'\n                      : 'text-muted-foreground'\n                  )}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                  whileHover={{ scale: 1.02, x: 4 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  {item.label}\n                </motion.button>\n              ))}\n            </nav>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAcO,SAAS,OAAO,EAAE,SAAS,EAAe;;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,MAAM,WAAW,0HAAA,CAAA,YAAS,CAAC,GAAG;kEAAC,CAAA,OAAQ,KAAK,EAAE;;oBAC9C,MAAM,iBAAiB,OAAO,OAAO,GAAG;oBAExC,KAAK,MAAM,WAAW,SAAU;wBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;wBACxC,IAAI,SAAS;4BACX,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG;4BACpC,IAAI,kBAAkB,aAAa,iBAAiB,YAAY,cAAc;gCAC5E,iBAAiB;gCACjB;4BACF;wBACF;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,YAAY,QAAQ,SAAS,GAAG,IAAI,4BAA4B;YACtE,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;QACA,oBAAoB;IACtB;IAEA,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA,aACI,0EACA,kBACJ;QAEF,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,6LAAC,mJAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;oCAAK,WAAU;8CAAmD;;;;;;;;;;;;sCAMrE,6LAAC;4BAAI,WAAU;sCACZ,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,qBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,eAAe,KAAK,EAAE;oCACrC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,2CACA,kBAAkB,KAAK,EAAE,GACrB,gCACA;oCAEN,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,KAAK,KAAK;mCAZN,KAAK,EAAE;;;;;;;;;;sCAkBlB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,uJAAA,CAAA,cAAW;;;;;8CAGZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,cAAW;8CAEX,cAAA,6LAAC,4LAAA,CAAA,kBAAe;wCAAC,MAAK;kDACnB,iCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,QAAQ,CAAC;gDAAI,SAAS;4CAAE;4CACnC,SAAS;gDAAE,QAAQ;gDAAG,SAAS;4CAAE;4CACjC,MAAM;gDAAE,QAAQ;gDAAI,SAAS;4CAAE;4CAC/B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;2CANT;;;;iEASN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,QAAQ;gDAAI,SAAS;4CAAE;4CAClC,SAAS;gDAAE,QAAQ;gDAAG,SAAS;4CAAE;4CACjC,MAAM;gDAAE,QAAQ,CAAC;gDAAI,SAAS;4CAAE;4CAChC,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;2CANZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBlB,6LAAC,4LAAA,CAAA,kBAAe;0BACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,6LAAC;wBAAI,WAAU;kCACZ,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,eAAe,KAAK,EAAE;gCACrC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,2CACA,kBAAkB,KAAK,EAAE,GACrB,gCACA;gCAEN,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,YAAY;oCAAE,OAAO;oCAAM,GAAG;gCAAE;gCAChC,UAAU;oCAAE,OAAO;gCAAK;0CAEvB,KAAK,KAAK;+BAfN,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAwB9B;GAnLgB;KAAA", "debugId": null}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/layout/mobile-menu.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { NAV_ITEMS } from '@/lib/constants';\n\ninterface MobileMenuProps {\n  className?: string;\n}\n\nexport function MobileMenu({ className }: MobileMenuProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [activeSection, setActiveSection] = useState('home');\n\n  // Handle active section detection\n  useEffect(() => {\n    const handleScroll = () => {\n      const sections = NAV_ITEMS.map(item => item.id);\n      const scrollPosition = window.scrollY + 100;\n\n      for (const section of sections) {\n        const element = document.getElementById(section);\n        if (element) {\n          const { offsetTop, offsetHeight } = element;\n          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {\n            setActiveSection(section);\n            break;\n          }\n        }\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Listen for mobile menu toggle events\n  useEffect(() => {\n    const handleMobileMenuToggle = () => {\n      setIsOpen(prev => !prev);\n    };\n\n    // Listen for clicks on mobile menu button\n    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');\n    if (mobileMenuBtn) {\n      mobileMenuBtn.addEventListener('click', handleMobileMenuToggle);\n      return () => mobileMenuBtn.removeEventListener('click', handleMobileMenuToggle);\n    }\n  }, []);\n\n  // Prevent body scroll when menu is open\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  const handleNavClick = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      const offsetTop = element.offsetTop - 80; // Account for navbar height\n      window.scrollTo({\n        top: offsetTop,\n        behavior: 'smooth'\n      });\n    }\n    setIsOpen(false);\n  };\n\n  const menuVariants = {\n    closed: {\n      x: '100%',\n      transition: {\n        type: 'spring',\n        stiffness: 400,\n        damping: 40,\n      }\n    },\n    open: {\n      x: 0,\n      transition: {\n        type: 'spring',\n        stiffness: 400,\n        damping: 40,\n      }\n    }\n  };\n\n  const overlayVariants = {\n    closed: {\n      opacity: 0,\n      transition: {\n        duration: 0.3,\n      }\n    },\n    open: {\n      opacity: 1,\n      transition: {\n        duration: 0.3,\n      }\n    }\n  };\n\n  const itemVariants = {\n    closed: {\n      x: 50,\n      opacity: 0,\n    },\n    open: (i: number) => ({\n      x: 0,\n      opacity: 1,\n      transition: {\n        delay: i * 0.1,\n        duration: 0.3,\n      }\n    })\n  };\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Overlay */}\n          <motion.div\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden\"\n            variants={overlayVariants}\n            initial=\"closed\"\n            animate=\"open\"\n            exit=\"closed\"\n            onClick={() => setIsOpen(false)}\n          />\n\n          {/* Mobile Menu */}\n          <motion.div\n            className={cn(\n              'fixed top-0 right-0 h-full w-80 max-w-[80vw] bg-background/95 backdrop-blur-md',\n              'border-l border-border/50 shadow-2xl z-50 md:hidden',\n              className\n            )}\n            variants={menuVariants}\n            initial=\"closed\"\n            animate=\"open\"\n            exit=\"closed\"\n          >\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-6 border-b border-border/50\">\n              <h2 className=\"text-lg font-semibold text-foreground\">Menu</h2>\n              <motion.button\n                onClick={() => setIsOpen(false)}\n                className=\"p-2 rounded-lg hover:bg-muted/50 transition-colors\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <X className=\"w-5 h-5\" />\n              </motion.button>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"p-6\">\n              <ul className=\"space-y-2\">\n                {NAV_ITEMS.map((item, index) => (\n                  <motion.li\n                    key={item.id}\n                    custom={index}\n                    variants={itemVariants}\n                    initial=\"closed\"\n                    animate=\"open\"\n                  >\n                    <motion.button\n                      onClick={() => handleNavClick(item.id)}\n                      className={cn(\n                        'w-full text-left px-4 py-3 rounded-lg text-base font-medium',\n                        'transition-all duration-200 hover:bg-muted/50',\n                        activeSection === item.id\n                          ? 'text-foreground bg-muted/30 border-l-4 border-primary'\n                          : 'text-muted-foreground hover:text-foreground'\n                      )}\n                      whileHover={{ \n                        scale: 1.02, \n                        x: 4,\n                        transition: { duration: 0.2 }\n                      }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <span className=\"capitalize\">{item.label}</span>\n                    </motion.button>\n                  </motion.li>\n                ))}\n              </ul>\n            </nav>\n\n            {/* Footer */}\n            <div className=\"absolute bottom-6 left-6 right-6\">\n              <div className=\"text-center text-sm text-muted-foreground\">\n                <p>© 2024 Narendra Chowdary</p>\n              </div>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAYO,SAAS,WAAW,EAAE,SAAS,EAAmB;;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,MAAM,WAAW,0HAAA,CAAA,YAAS,CAAC,GAAG;sEAAC,CAAA,OAAQ,KAAK,EAAE;;oBAC9C,MAAM,iBAAiB,OAAO,OAAO,GAAG;oBAExC,KAAK,MAAM,WAAW,SAAU;wBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;wBACxC,IAAI,SAAS;4BACX,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG;4BACpC,IAAI,kBAAkB,aAAa,iBAAiB,YAAY,cAAc;gCAC5E,iBAAiB;gCACjB;4BACF;wBACF;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;+DAAyB;oBAC7B;uEAAU,CAAA,OAAQ,CAAC;;gBACrB;;YAEA,0CAA0C;YAC1C,MAAM,gBAAgB,SAAS,aAAa,CAAC;YAC7C,IAAI,eAAe;gBACjB,cAAc,gBAAgB,CAAC,SAAS;gBACxC;4CAAO,IAAM,cAAc,mBAAmB,CAAC,SAAS;;YAC1D;QACF;+BAAG,EAAE;IAEL,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;wCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;+BAAG;QAAC;KAAO;IAEX,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,MAAM,YAAY,QAAQ,SAAS,GAAG,IAAI,4BAA4B;YACtE,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;QACF;QACA,UAAU;IACZ;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YACN,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;QACA,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,GAAG;YACH,SAAS;QACX;QACA,MAAM,CAAC,IAAc,CAAC;gBACpB,GAAG;gBACH,SAAS;gBACT,YAAY;oBACV,OAAO,IAAI;oBACX,UAAU;gBACZ;YACF,CAAC;IACH;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,UAAU;;;;;;8BAI3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kFACA,uDACA;oBAEF,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,MAAK;;sCAGL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS,IAAM,UAAU;oCACzB,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CACX,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCAER,QAAQ;wCACR,UAAU;wCACV,SAAQ;wCACR,SAAQ;kDAER,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS,IAAM,eAAe,KAAK,EAAE;4CACrC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,iDACA,kBAAkB,KAAK,EAAE,GACrB,0DACA;4CAEN,YAAY;gDACV,OAAO;gDACP,GAAG;gDACH,YAAY;oDAAE,UAAU;gDAAI;4CAC9B;4CACA,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,6LAAC;gDAAK,WAAU;0DAAc,KAAK,KAAK;;;;;;;;;;;uCAtBrC,KAAK,EAAE;;;;;;;;;;;;;;;sCA8BpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GAtMgB;KAAA", "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/contexts/landing-context.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState } from 'react';\n\ninterface LandingContextType {\n  showLanding: boolean;\n  showHeader: boolean;\n  setShowLanding: (show: boolean) => void;\n  setShowHeader: (show: boolean) => void;\n  handleLandingComplete: () => void;\n}\n\nconst LandingContext = createContext<LandingContextType | undefined>(undefined);\n\nexport function LandingProvider({ children }: { children: React.ReactNode }) {\n  const [showLanding, setShowLanding] = useState(true);\n  const [showHeader, setShowHeader] = useState(false);\n\n  const handleLandingComplete = () => {\n    setShowLanding(false);\n    // Show header after landing screen disappears with proper timing\n    setTimeout(() => {\n      setShowHeader(true);\n    }, 200);\n  };\n\n  return (\n    <LandingContext.Provider\n      value={{\n        showLanding,\n        showHeader,\n        setShowLanding,\n        setShowHeader,\n        handleLandingComplete,\n      }}\n    >\n      {children}\n    </LandingContext.Provider>\n  );\n}\n\nexport function useLanding() {\n  const context = useContext(LandingContext);\n  if (context === undefined) {\n    throw new Error('useLanding must be used within a LandingProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAYA,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,SAAS,gBAAgB,EAAE,QAAQ,EAAiC;;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,wBAAwB;QAC5B,eAAe;QACf,iEAAiE;QACjE,WAAW;YACT,cAAc;QAChB,GAAG;IACL;IAEA,qBACE,6LAAC,eAAe,QAAQ;QACtB,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAzBgB;KAAA;AA2BT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/interactive/particle-animation.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { PARTICLE_CONFIG } from '@/lib/constants';\n\ninterface ParticleAnimationProps {\n  className?: string;\n}\n\nexport function ParticleAnimation({ className }: ParticleAnimationProps) {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const animationRef = useRef<number>();\n  const particlesRef = useRef<Array<{\n    x: number;\n    y: number;\n    vx: number;\n    vy: number;\n    size: number;\n    opacity: number;\n    color: string;\n  }>>([]);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Initialize particles\n    const initParticles = () => {\n      particlesRef.current = [];\n      for (let i = 0; i < PARTICLE_CONFIG.count; i++) {\n        particlesRef.current.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          vx: (Math.random() - 0.5) * PARTICLE_CONFIG.speed,\n          vy: (Math.random() - 0.5) * PARTICLE_CONFIG.speed,\n          size: Math.random() * (PARTICLE_CONFIG.maxSize - PARTICLE_CONFIG.minSize) + PARTICLE_CONFIG.minSize,\n          opacity: Math.random() * (PARTICLE_CONFIG.opacity.max - PARTICLE_CONFIG.opacity.min) + PARTICLE_CONFIG.opacity.min,\n          color: PARTICLE_CONFIG.colors[Math.floor(Math.random() * PARTICLE_CONFIG.colors.length)],\n        });\n      }\n    };\n\n    initParticles();\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      particlesRef.current.forEach((particle) => {\n        // Update position\n        particle.x += particle.vx;\n        particle.y += particle.vy;\n\n        // Wrap around edges\n        if (particle.x < 0) particle.x = canvas.width;\n        if (particle.x > canvas.width) particle.x = 0;\n        if (particle.y < 0) particle.y = canvas.height;\n        if (particle.y > canvas.height) particle.y = 0;\n\n        // Draw particle\n        ctx.save();\n        ctx.globalAlpha = particle.opacity;\n        ctx.fillStyle = particle.color;\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.restore();\n      });\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <motion.canvas\n      ref={canvasRef}\n      className={`fixed inset-0 pointer-events-none z-0 ${className}`}\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 1 }}\n      style={{\n        background: 'transparent',\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,kBAAkB,EAAE,SAAS,EAA0B;;IACrE,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAQtB,EAAE;IAEN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;YAEV,kBAAkB;YAClB,MAAM;4DAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,UAAU;oBAChC,OAAO,MAAM,GAAG,OAAO,WAAW;gBACpC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAElC,uBAAuB;YACvB,MAAM;6DAAgB;oBACpB,aAAa,OAAO,GAAG,EAAE;oBACzB,IAAK,IAAI,IAAI,GAAG,IAAI,0HAAA,CAAA,kBAAe,CAAC,KAAK,EAAE,IAAK;wBAC9C,aAAa,OAAO,CAAC,IAAI,CAAC;4BACxB,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;4BAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;4BAChC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,0HAAA,CAAA,kBAAe,CAAC,KAAK;4BACjD,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,0HAAA,CAAA,kBAAe,CAAC,KAAK;4BACjD,MAAM,KAAK,MAAM,KAAK,CAAC,0HAAA,CAAA,kBAAe,CAAC,OAAO,GAAG,0HAAA,CAAA,kBAAe,CAAC,OAAO,IAAI,0HAAA,CAAA,kBAAe,CAAC,OAAO;4BACnG,SAAS,KAAK,MAAM,KAAK,CAAC,0HAAA,CAAA,kBAAe,CAAC,OAAO,CAAC,GAAG,GAAG,0HAAA,CAAA,kBAAe,CAAC,OAAO,CAAC,GAAG,IAAI,0HAAA,CAAA,kBAAe,CAAC,OAAO,CAAC,GAAG;4BAClH,OAAO,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,0HAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,MAAM,EAAE;wBAC1F;oBACF;gBACF;;YAEA;YAEA,iBAAiB;YACjB,MAAM;uDAAU;oBACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE/C,aAAa,OAAO,CAAC,OAAO;+DAAC,CAAC;4BAC5B,kBAAkB;4BAClB,SAAS,CAAC,IAAI,SAAS,EAAE;4BACzB,SAAS,CAAC,IAAI,SAAS,EAAE;4BAEzB,oBAAoB;4BACpB,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,KAAK;4BAC7C,IAAI,SAAS,CAAC,GAAG,OAAO,KAAK,EAAE,SAAS,CAAC,GAAG;4BAC5C,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,MAAM;4BAC9C,IAAI,SAAS,CAAC,GAAG,OAAO,MAAM,EAAE,SAAS,CAAC,GAAG;4BAE7C,gBAAgB;4BAChB,IAAI,IAAI;4BACR,IAAI,WAAW,GAAG,SAAS,OAAO;4BAClC,IAAI,SAAS,GAAG,SAAS,KAAK;4BAC9B,IAAI,SAAS;4BACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;4BAC5D,IAAI,IAAI;4BACR,IAAI,OAAO;wBACb;;oBAEA,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;;YAEA;YAEA;+CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;gBACF;;QACF;sCAAG,EAAE;IAEL,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW,CAAC,sCAAsC,EAAE,WAAW;QAC/D,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAE;QAC1B,OAAO;YACL,YAAY;QACd;;;;;;AAGN;GAjGgB;KAAA", "debugId": null}}, {"offset": {"line": 1711, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/layout/layout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useContext } from 'react';\nimport { Navbar } from './navbar';\nimport { MobileMenu } from './mobile-menu';\nimport { ThemeProvider, ThemeContext } from '@/contexts/theme-context';\nimport { LandingProvider, useLanding } from '@/contexts/landing-context';\nimport { ParticleAnimation } from '@/components/interactive/particle-animation';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nfunction LayoutContent({ children }: LayoutProps) {\n  const themeContext = useContext(ThemeContext);\n  const landingContext = useLanding();\n\n  // Safely handle theme context\n  const theme = themeContext?.theme || 'light';\n  const { showHeader } = landingContext;\n\n  return (\n    <div className=\"min-h-screen bg-background text-foreground transition-colors duration-300\">\n      {/* Particle Animation for Dark Mode */}\n      {theme === 'dark' && <ParticleAnimation />}\n\n      {/* Navigation - Only show after landing screen */}\n      {showHeader && (\n        <>\n          <Navbar />\n          <MobileMenu />\n        </>\n      )}\n\n      {/* Main Content */}\n      <main className={`relative z-10 ${showHeader ? 'pt-20' : ''}`}>\n        {children}\n      </main>\n    </div>\n  );\n}\n\nexport function Layout({ children }: LayoutProps) {\n  return (\n    <ThemeProvider>\n      <LandingProvider>\n        <LayoutContent>{children}</LayoutContent>\n      </LandingProvider>\n    </ThemeProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AAaA,SAAS,cAAc,EAAE,QAAQ,EAAe;;IAC9C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,uIAAA,CAAA,eAAY;IAC5C,MAAM,iBAAiB,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD;IAEhC,8BAA8B;IAC9B,MAAM,QAAQ,cAAc,SAAS;IACrC,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,UAAU,wBAAU,6LAAC,6JAAA,CAAA,oBAAiB;;;;;YAGtC,4BACC;;kCACE,6LAAC,yIAAA,CAAA,SAAM;;;;;kCACP,6LAAC,iJAAA,CAAA,aAAU;;;;;;;0BAKf,6LAAC;gBAAK,WAAW,CAAC,cAAc,EAAE,aAAa,UAAU,IAAI;0BAC1D;;;;;;;;;;;;AAIT;GA3BS;;QAEgB,yIAAA,CAAA,aAAU;;;KAF1B;AA6BF,SAAS,OAAO,EAAE,QAAQ,EAAe;IAC9C,qBACE,6LAAC,uIAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC,yIAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC;0BAAe;;;;;;;;;;;;;;;;AAIxB;MARgB", "debugId": null}}]}