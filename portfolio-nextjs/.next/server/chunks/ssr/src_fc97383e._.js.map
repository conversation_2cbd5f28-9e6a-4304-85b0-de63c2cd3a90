{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/sections/landing-screen.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { LANGUAGES, ANIMATION_DURATIONS } from '@/lib/constants';\n\ninterface LandingScreenProps {\n  onComplete?: () => void;\n}\n\nexport function LandingScreen({ onComplete }: LandingScreenProps) {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isVisible, setIsVisible] = useState(true);\n  const [showParticles, setShowParticles] = useState(false);\n\n  // Multi-language animation effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % LANGUAGES.length);\n    }, ANIMATION_DURATIONS.languageSwitch);\n\n    // Start exit animation after showing all languages\n    const exitTimer = setTimeout(() => {\n      setShowParticles(true);\n      \n      // Start exit animation\n      setTimeout(() => {\n        setIsVisible(false);\n        \n        // Call onComplete callback after animation\n        setTimeout(() => {\n          onComplete?.();\n        }, 800);\n      }, 300);\n    }, 1500);\n\n    return () => {\n      clearInterval(interval);\n      clearTimeout(exitTimer);\n    };\n  }, [onComplete]);\n\n  // Background panning animation variants\n  const backgroundVariants = {\n    animate: {\n      x: ['-25%', '-75%'],\n      y: ['-25%', '-75%'],\n      transition: {\n        duration: 5,\n        repeat: Infinity,\n        ease: 'linear',\n      },\n    },\n  };\n\n  // Landing screen exit animation\n  const landingVariants = {\n    visible: {\n      y: 0,\n      rotateX: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5,\n        ease: 'easeOut',\n      },\n    },\n    hidden: {\n      y: '-100%',\n      rotateX: 10,\n      opacity: 0,\n      transition: {\n        duration: 1.2,\n        ease: [0.6, 0.1, 0.3, 1],\n      },\n    },\n  };\n\n  // Message animation variants\n  const messageVariants = {\n    hidden: {\n      opacity: 0,\n      scale: 1,\n      transition: {\n        duration: 0.3,\n        ease: 'easeInOut',\n      },\n    },\n    visible: {\n      opacity: 1,\n      scale: 1.1,\n      transition: {\n        duration: 0.3,\n        ease: 'easeInOut',\n      },\n    },\n  };\n\n  // Particle animation variants\n  const particleVariants = {\n    initial: {\n      scale: 0,\n      opacity: 0,\n    },\n    animate: {\n      scale: 1,\n      opacity: 0.7,\n      x: Math.random() * 200 - 100,\n      y: Math.random() * 200 - 100,\n      transition: {\n        duration: 1.5,\n        ease: 'easeOut',\n      },\n    },\n    exit: {\n      scale: 0,\n      opacity: 0,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed inset-0 z-[2000] flex items-center justify-center overflow-hidden\"\n          style={{\n            background: 'linear-gradient(135deg, #000, #222)',\n            perspective: '1000px',\n          }}\n          variants={landingVariants}\n          initial=\"visible\"\n          animate=\"visible\"\n          exit=\"hidden\"\n        >\n          {/* Background Effect */}\n          <motion.div\n            className=\"absolute w-[200%] h-[200%] opacity-20\"\n            style={{\n              background: 'radial-gradient(circle, rgba(255,255,255,0.08) 0%, rgba(0,0,0,0) 70%)',\n            }}\n            variants={backgroundVariants}\n            animate=\"animate\"\n          />\n\n          {/* Message Container */}\n          <div className=\"relative h-[120px] w-full text-center\">\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={currentIndex}\n                className=\"absolute inset-0 flex items-center justify-center\"\n                variants={messageVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                exit=\"hidden\"\n              >\n                <h1 \n                  className=\"text-5xl md:text-6xl font-semibold text-white/95\"\n                  style={{\n                    textShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',\n                  }}\n                >\n                  {LANGUAGES[currentIndex].greeting}\n                </h1>\n              </motion.div>\n            </AnimatePresence>\n          </div>\n\n          {/* Particles Effect */}\n          <AnimatePresence>\n            {showParticles && (\n              <>\n                {Array.from({ length: 20 }).map((_, i) => (\n                  <motion.div\n                    key={i}\n                    className=\"absolute w-1.5 h-1.5 bg-white/70 rounded-full pointer-events-none\"\n                    style={{\n                      left: `${50 + (Math.random() - 0.5) * 20}%`,\n                      top: `${50 + (Math.random() - 0.5) * 20}%`,\n                    }}\n                    variants={particleVariants}\n                    initial=\"initial\"\n                    animate=\"animate\"\n                    exit=\"exit\"\n                  />\n                ))}\n              </>\n            )}\n          </AnimatePresence>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAUO,SAAS,cAAc,EAAE,UAAU,EAAsB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,uHAAA,CAAA,YAAS,CAAC,MAAM;QACzD,GAAG,uHAAA,CAAA,sBAAmB,CAAC,cAAc;QAErC,mDAAmD;QACnD,MAAM,YAAY,WAAW;YAC3B,iBAAiB;YAEjB,uBAAuB;YACvB,WAAW;gBACT,aAAa;gBAEb,2CAA2C;gBAC3C,WAAW;oBACT;gBACF,GAAG;YACL,GAAG;QACL,GAAG;QAEH,OAAO;YACL,cAAc;YACd,aAAa;QACf;IACF,GAAG;QAAC;KAAW;IAEf,wCAAwC;IACxC,MAAM,qBAAqB;QACzB,SAAS;YACP,GAAG;gBAAC;gBAAQ;aAAO;YACnB,GAAG;gBAAC;gBAAQ;aAAO;YACnB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,SAAS;YACP,GAAG;YACH,SAAS;YACT,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;YACH,SAAS;YACT,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAK;oBAAK;oBAAK;iBAAE;YAC1B;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,SAAS;YACP,SAAS;YACT,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM,mBAAmB;QACvB,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,SAAS;YACP,OAAO;YACP,SAAS;YACT,GAAG,KAAK,MAAM,KAAK,MAAM;YACzB,GAAG,KAAK,MAAM,KAAK,MAAM;YACzB,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,MAAM;YACJ,OAAO;YACP,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,aAAa;YACf;YACA,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,MAAK;;8BAGL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBACL,YAAY;oBACd;oBACA,UAAU;oBACV,SAAQ;;;;;;8BAIV,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;sCAEL,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY;gCACd;0CAEC,uHAAA,CAAA,YAAS,CAAC,aAAa,CAAC,QAAQ;;;;;;2BAb9B;;;;;;;;;;;;;;;8BAoBX,8OAAC,yLAAA,CAAA,kBAAe;8BACb,+BACC;kCACG,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;oCAC3C,KAAK,GAAG,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;gCAC5C;gCACA,UAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,MAAK;+BATA;;;;;;;;;;;;;;;;;;;;;;AAmBzB", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/interactive/scroll-indicator.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ChevronDown } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { SCROLL_CONFIG } from '@/lib/constants';\n\ninterface ScrollIndicatorProps {\n  className?: string;\n}\n\nexport function ScrollIndicator({ className }: ScrollIndicatorProps) {\n  const [isVisible, setIsVisible] = useState(true);\n  const [isOnHomePage, setIsOnHomePage] = useState(true);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollY = window.scrollY;\n      const homeSection = document.getElementById('home');\n      \n      if (homeSection) {\n        const homeSectionHeight = homeSection.offsetHeight;\n        const isInHomeSection = scrollY < homeSectionHeight - 100;\n        \n        setIsOnHomePage(isInHomeSection);\n        setIsVisible(isInHomeSection && scrollY < SCROLL_CONFIG.indicatorFadeDistance);\n      }\n    };\n\n    // Initial check\n    handleScroll();\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleClick = () => {\n    const aboutSection = document.getElementById('about');\n    if (aboutSection) {\n      aboutSection.scrollIntoView({ \n        behavior: 'smooth',\n        block: 'start'\n      });\n    }\n  };\n\n  const containerVariants = {\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5,\n        ease: 'easeOut',\n      },\n    },\n    hidden: {\n      opacity: 0,\n      y: 20,\n      transition: {\n        duration: 0.3,\n        ease: 'easeIn',\n      },\n    },\n  };\n\n  const arrowVariants = {\n    animate: {\n      y: [0, 8, 0],\n      transition: {\n        duration: 1.5,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      },\n    },\n  };\n\n  const textVariants = {\n    animate: {\n      opacity: [0.7, 1, 0.7],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      },\n    },\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && isOnHomePage && (\n        <motion.div\n          className={cn(\n            'absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center cursor-pointer z-10',\n            className\n          )}\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"hidden\"\n          onClick={handleClick}\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          {/* Scroll Text */}\n          <motion.span\n            className=\"text-sm text-muted-foreground mb-2 font-medium\"\n            variants={textVariants}\n            animate=\"animate\"\n          >\n            Scroll Down\n          </motion.span>\n\n          {/* Arrow Container */}\n          <div className=\"relative\">\n            {/* First Arrow */}\n            <motion.div\n              variants={arrowVariants}\n              animate=\"animate\"\n            >\n              <ChevronDown className=\"w-6 h-6 text-muted-foreground\" />\n            </motion.div>\n\n            {/* Second Arrow (offset) */}\n            <motion.div\n              className=\"absolute top-0 left-0\"\n              variants={arrowVariants}\n              animate=\"animate\"\n              style={{ marginTop: '-8px' }}\n              transition={{ delay: 0.2 }}\n            >\n              <ChevronDown className=\"w-6 h-6 text-muted-foreground opacity-60\" />\n            </motion.div>\n          </div>\n\n          {/* Glow effect on hover */}\n          <motion.div\n            className=\"absolute inset-0 rounded-full bg-primary/20 blur-xl opacity-0\"\n            whileHover={{ opacity: 1 }}\n            transition={{ duration: 0.3 }}\n          />\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,gBAAgB,EAAE,SAAS,EAAwB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,UAAU,OAAO,OAAO;YAC9B,MAAM,cAAc,SAAS,cAAc,CAAC;YAE5C,IAAI,aAAa;gBACf,MAAM,oBAAoB,YAAY,YAAY;gBAClD,MAAM,kBAAkB,UAAU,oBAAoB;gBAEtD,gBAAgB;gBAChB,aAAa,mBAAmB,UAAU,uHAAA,CAAA,gBAAa,CAAC,qBAAqB;YAC/E;QACF;QAEA,gBAAgB;QAChB;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,eAAe,SAAS,cAAc,CAAC;QAC7C,IAAI,cAAc;YAChB,aAAa,cAAc,CAAC;gBAC1B,UAAU;gBACV,OAAO;YACT;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,QAAQ;YACN,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,SAAS;YACP,GAAG;gBAAC;gBAAG;gBAAG;aAAE;YACZ,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YACP,SAAS;gBAAC;gBAAK;gBAAG;aAAI;YACtB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,aAAa,8BACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wGACA;YAEF,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,SAAS;YACT,YAAY;gBAAE,OAAO;YAAI;YACzB,UAAU;gBAAE,OAAO;YAAK;;8BAGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,WAAU;oBACV,UAAU;oBACV,SAAQ;8BACT;;;;;;8BAKD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAIzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;4BACV,SAAQ;4BACR,OAAO;gCAAE,WAAW;4BAAO;4BAC3B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,SAAS;oBAAE;oBACzB,YAAY;wBAAE,UAAU;oBAAI;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/common/social-icons.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Github, Linkedin, Twitter, Mail } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { SOCIAL_LINKS } from '@/lib/constants';\n\ninterface SocialIconsProps {\n  className?: string;\n}\n\nexport function SocialIcons({ className }: SocialIconsProps) {\n  const iconMap = {\n    Github,\n    Linkedin,\n    Twitter,\n    Mail,\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      className={cn('flex items-center gap-4', className)}\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {SOCIAL_LINKS.map((link, index) => {\n        const IconComponent = iconMap[link.icon as keyof typeof iconMap];\n        \n        return (\n          <motion.a\n            key={link.platform}\n            href={link.url}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"group relative p-3 rounded-full bg-muted/50 hover:bg-muted transition-all duration-300\"\n            variants={itemVariants}\n            whileHover={{ \n              scale: 1.1,\n              rotate: 5,\n            }}\n            whileTap={{ scale: 0.95 }}\n            aria-label={link.label}\n          >\n            {/* Background glow effect */}\n            <motion.div\n              className=\"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              initial={false}\n              animate={{\n                scale: [1, 1.2, 1],\n              }}\n              transition={{\n                duration: 2,\n                repeat: Infinity,\n                ease: 'easeInOut',\n              }}\n            />\n            \n            {/* Icon */}\n            <IconComponent className=\"w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors duration-300 relative z-10\" />\n            \n            {/* Tooltip */}\n            <motion.div\n              className=\"absolute -top-12 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap\"\n              initial={{ y: 10, opacity: 0 }}\n              whileHover={{ y: 0, opacity: 1 }}\n            >\n              {link.platform}\n              <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover\" />\n            </motion.div>\n          </motion.a>\n        );\n      })}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;AAYO,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,UAAU;QACd,QAAA,sMAAA,CAAA,SAAM;QACN,UAAA,0MAAA,CAAA,WAAQ;QACR,SAAA,wMAAA,CAAA,UAAO;QACP,MAAA,kMAAA,CAAA,OAAI;IACN;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACzC,UAAU;QACV,SAAQ;QACR,SAAQ;kBAEP,uHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,MAAM;YACvB,MAAM,gBAAgB,OAAO,CAAC,KAAK,IAAI,CAAyB;YAEhE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBAEP,MAAM,KAAK,GAAG;gBACd,QAAO;gBACP,KAAI;gBACJ,WAAU;gBACV,UAAU;gBACV,YAAY;oBACV,OAAO;oBACP,QAAQ;gBACV;gBACA,UAAU;oBAAE,OAAO;gBAAK;gBACxB,cAAY,KAAK,KAAK;;kCAGtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;wBACT,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,8OAAC;wBAAc,WAAU;;;;;;kCAGzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,YAAY;4BAAE,GAAG;4BAAG,SAAS;wBAAE;;4BAE9B,KAAK,QAAQ;0CACd,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;eArCZ,KAAK,QAAQ;;;;;QAyCxB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/sections/hero-section.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { <PERSON>R<PERSON>, Code, Github, Linkedin, Twitter } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { ScrollIndicator } from '@/components/interactive/scroll-indicator';\nimport { SocialIcons } from '@/components/common/social-icons';\n\ninterface HeroSectionProps {\n  className?: string;\n}\n\nexport function HeroSection({ className }: HeroSectionProps) {\n  const handleGetInTouch = () => {\n    const contactSection = document.getElementById('contact');\n    if (contactSection) {\n      contactSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  const handleViewProjects = () => {\n    const projectsSection = document.getElementById('projects');\n    if (projectsSection) {\n      projectsSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n\n\n  return (\n    <section\n      id=\"home\"\n      className={cn(\n        'min-h-screen flex items-center justify-center relative overflow-hidden',\n        className\n      )}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\">\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-screen\">\n          {/* Text Container */}\n          <motion.div\n            className=\"text-container space-y-8 lg:pr-8\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            {/* Online Status */}\n            <motion.div\n              className=\"inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm font-medium\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 0.4 }}\n            >\n              <motion.span\n                className=\"w-2 h-2 bg-green-500 rounded-full\"\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [1, 0.7, 1],\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: 'easeInOut',\n                }}\n              />\n              Available for work\n            </motion.div>\n\n            {/* Main Heading */}\n            <motion.h1\n              className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n            >\n              Hi, I'm Narendra{' '}\n              <motion.span\n                className=\"inline-block\"\n                animate={{\n                  rotate: [0, 14, -8, 14, -4, 10, 0],\n                }}\n                transition={{\n                  duration: 2.5,\n                  repeat: Infinity,\n                  repeatDelay: 3,\n                  ease: 'easeInOut',\n                }}\n              >\n                👋\n              </motion.span>\n            </motion.h1>\n\n            {/* Bio Container */}\n            <motion.div\n              className=\"bio-container space-y-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.8 }}\n            >\n              <h2 className=\"text-xl sm:text-2xl font-semibold text-foreground\">\n                No-Code Developer | AI Prompt Engineer | Mobile App Creator\n              </h2>\n              <p className=\"text-lg text-muted-foreground leading-relaxed max-w-2xl\">\n                Recent Computer Science and Engineering graduate specializing in AI-assisted development and no-code solutions.\n                I leverage modern AI tools to build efficient SaaS applications, mobile apps, and automation workflows.\n                With expertise in cloud backends and API integration, I create cost-effective digital solutions with minimal traditional coding.\n              </p>\n            </motion.div>\n\n            {/* Button Container */}\n            <motion.div\n              className=\"button-container flex flex-col sm:flex-row gap-4 mt-8 pl-0 sm:pl-[100px]\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.0 }}\n            >\n              <motion.button\n                onClick={handleGetInTouch}\n                className=\"button get-in-touch group relative px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span className=\"relative z-10 flex items-center gap-2\">\n                  Get in Touch\n                  <ArrowRight className=\"w-4 h-4 transition-transform group-hover:translate-x-1\" />\n                </span>\n                <motion.div\n                  className=\"absolute inset-0 bg-gradient-to-r from-primary to-primary/80\"\n                  initial={{ x: '-100%' }}\n                  whileHover={{ x: 0 }}\n                  transition={{ duration: 0.3 }}\n                />\n              </motion.button>\n\n              <motion.button\n                onClick={handleViewProjects}\n                className=\"button view-projects group relative px-8 py-3 border border-border text-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-primary/50\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span className=\"relative z-10 flex items-center gap-2\">\n                  View Projects\n                  <Code className=\"w-4 h-4 transition-transform group-hover:rotate-12\" />\n                </span>\n                <motion.div\n                  className=\"absolute inset-0 bg-muted/50\"\n                  initial={{ x: '-100%' }}\n                  whileHover={{ x: 0 }}\n                  transition={{ duration: 0.3 }}\n                />\n              </motion.button>\n\n\n            </motion.div>\n\n            {/* Social Icons */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.2 }}\n            >\n              <SocialIcons />\n            </motion.div>\n          </motion.div>\n\n          {/* Image Container */}\n          <motion.div\n            className=\"image-container w-full lg:w-[50%] flex justify-center lg:justify-end\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1, delay: 0.4 }}\n          >\n            <motion.div\n              className=\"morph-container group relative w-[300px] h-[300px] overflow-hidden bg-cover bg-no-repeat bg-center border-4 border-[#2d2e32]\"\n                style={{\n                  borderRadius: '60% 40% 30% 70%/60% 30% 70% 40%',\n                  backgroundImage: `url('data:image/svg+xml,${encodeURIComponent(`\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 300 300\">\n                      <defs>\n                        <linearGradient id=\"grad1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                          <stop offset=\"0%\" style=\"stop-color:#667eea;stop-opacity:1\" />\n                          <stop offset=\"100%\" style=\"stop-color:#764ba2;stop-opacity:1\" />\n                        </linearGradient>\n                      </defs>\n                      <rect width=\"300\" height=\"300\" fill=\"url(#grad1)\" />\n                      <circle cx=\"150\" cy=\"120\" r=\"40\" fill=\"rgba(255,255,255,0.1)\" />\n                      <ellipse cx=\"150\" cy=\"200\" rx=\"60\" ry=\"80\" fill=\"rgba(255,255,255,0.05)\" />\n                      <text x=\"150\" y=\"160\" text-anchor=\"middle\" fill=\"white\" font-size=\"48\">👨‍💻</text>\n                    </svg>\n                  `)}')`,\n                }}\n                animate={{\n                  borderRadius: [\n                    '60% 40% 30% 70%/60% 30% 70% 40%',\n                    '40% 60% 70% 30%/40% 70% 30% 60%',\n                    '30% 60% 70% 40%/50% 60% 30% 40%',\n                    '55% 45% 40% 60%/45% 30% 60% 55%',\n                    '60% 40% 30% 70%/60% 30% 70% 40%',\n                  ],\n                }}\n                transition={{\n                  duration: 8,\n                  repeat: Infinity,\n                  ease: 'easeInOut',\n                }}\n                whileHover={{\n                  scale: 1.05,\n                }}\n              >\n                {/* Background Image */}\n                <div\n                  className=\"background-image absolute top-0 left-0 w-full h-full object-cover z-[1] transition-opacity duration-500 ease-in-out group-hover:opacity-0\"\n                  style={{\n                    backgroundImage: `url('data:image/svg+xml,${encodeURIComponent(`\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 300 300\">\n                        <defs>\n                          <linearGradient id=\"grad1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                            <stop offset=\"0%\" style=\"stop-color:#667eea;stop-opacity:1\" />\n                            <stop offset=\"100%\" style=\"stop-color:#764ba2;stop-opacity:1\" />\n                          </linearGradient>\n                        </defs>\n                        <rect width=\"300\" height=\"300\" fill=\"url(#grad1)\" />\n                        <circle cx=\"150\" cy=\"120\" r=\"40\" fill=\"rgba(255,255,255,0.1)\" />\n                        <ellipse cx=\"150\" cy=\"200\" rx=\"60\" ry=\"80\" fill=\"rgba(255,255,255,0.05)\" />\n                        <text x=\"150\" y=\"160\" text-anchor=\"middle\" fill=\"white\" font-size=\"48\">👨‍💻</text>\n                      </svg>\n                    `)}')`,\n                    backgroundSize: 'cover',\n                    backgroundPosition: 'center',\n                    backgroundRepeat: 'no-repeat',\n                  }}\n                />\n\n                {/* Second Image - Shows on hover */}\n                <div\n                  className=\"second-image absolute top-0 left-0 w-full h-full object-cover z-[2] opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100\"\n                  style={{\n                    backgroundImage: `url('data:image/svg+xml,${encodeURIComponent(`\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 300 300\">\n                        <defs>\n                          <linearGradient id=\"grad2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                            <stop offset=\"0%\" style=\"stop-color:#f093fb;stop-opacity:1\" />\n                            <stop offset=\"100%\" style=\"stop-color:#f5576c;stop-opacity:1\" />\n                          </linearGradient>\n                        </defs>\n                        <rect width=\"300\" height=\"300\" fill=\"url(#grad2)\" />\n                        <circle cx=\"150\" cy=\"120\" r=\"40\" fill=\"rgba(255,255,255,0.1)\" />\n                        <ellipse cx=\"150\" cy=\"200\" rx=\"60\" ry=\"80\" fill=\"rgba(255,255,255,0.05)\" />\n                        <text x=\"150\" y=\"160\" text-anchor=\"middle\" fill=\"white\" font-size=\"48\">🌟</text>\n                      </svg>\n                    `)}')`,\n                    backgroundSize: 'cover',\n                    backgroundPosition: 'center',\n                    backgroundRepeat: 'no-repeat',\n                  }}\n                />\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <ScrollIndicator />\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;AAaO,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,mBAAmB;QACvB,MAAM,iBAAiB,SAAS,cAAc,CAAC;QAC/C,IAAI,gBAAgB;YAClB,eAAe,cAAc,CAAC;gBAAE,UAAU;YAAS;QACrD;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,kBAAkB,SAAS,cAAc,CAAC;QAChD,IAAI,iBAAiB;YACnB,gBAAgB,cAAc,CAAC;gBAAE,UAAU;YAAS;QACtD;IACF;IAIA,qBACE,8OAAC;QACC,IAAG;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAGxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAU;4CACV,SAAS;gDACP,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;gDAClB,SAAS;oDAAC;oDAAG;oDAAK;iDAAE;4CACtB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;;;;;;wCACA;;;;;;;8CAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;wCACzC;wCACkB;sDACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAU;4CACV,SAAS;gDACP,QAAQ;oDAAC;oDAAG;oDAAI,CAAC;oDAAG;oDAAI,CAAC;oDAAG;oDAAI;iDAAE;4CACpC;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,aAAa;gDACb,MAAM;4CACR;sDACD;;;;;;;;;;;;8CAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,8OAAC;4CAAE,WAAU;sDAA0D;;;;;;;;;;;;8CAQzE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,8OAAC;oDAAK,WAAU;;wDAAwC;sEAEtD,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;8DAExB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,GAAG;oDAAQ;oDACtB,YAAY;wDAAE,GAAG;oDAAE;oDACnB,YAAY;wDAAE,UAAU;oDAAI;;;;;;;;;;;;sDAIhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,8OAAC;oDAAK,WAAU;;wDAAwC;sEAEtD,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;8DAElB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,GAAG;oDAAQ;oDACtB,YAAY;wDAAE,GAAG;oDAAE;oDACnB,YAAY;wDAAE,UAAU;oDAAI;;;;;;;;;;;;;;;;;;8CAQlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC,+IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAKhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;sCAEtC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACR,OAAO;oCACL,cAAc;oCACd,iBAAiB,CAAC,wBAAwB,EAAE,mBAAmB,CAAC;;;;;;;;;;;;;kBAahE,CAAC,EAAE,EAAE,CAAC;gCACR;gCACA,SAAS;oCACP,cAAc;wCACZ;wCACA;wCACA;wCACA;wCACA;qCACD;gCACH;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;gCACA,YAAY;oCACV,OAAO;gCACT;;kDAGA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB,CAAC,wBAAwB,EAAE,mBAAmB,CAAC;;;;;;;;;;;;;oBAahE,CAAC,EAAE,EAAE,CAAC;4CACN,gBAAgB;4CAChB,oBAAoB;4CACpB,kBAAkB;wCACpB;;;;;;kDAIF,8OAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB,CAAC,wBAAwB,EAAE,mBAAmB,CAAC;;;;;;;;;;;;;oBAahE,CAAC,EAAE,EAAE,CAAC;4CACN,gBAAgB;4CAChB,oBAAoB;4CACpB,kBAAkB;wCACpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQZ,8OAAC,wJAAA,CAAA,kBAAe;;;;;;;;;;;AAGtB", "debugId": null}}, {"offset": {"line": 1052, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/sections/about-section.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { GraduationCap, Calendar, Award } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface AboutSectionProps {\n  className?: string;\n}\n\nexport function AboutSection({ className }: AboutSectionProps) {\n  const educationData = [\n    {\n      year: 'Expected 2026',\n      degree: 'Bachelor of Engineering in Computer Science and Engineering',\n      institution: 'NBKRIST College Autonomous',\n      grade: 'CGPA: 8.2 (Current)',\n    },\n    {\n      year: 'Graduated 2022',\n      degree: 'Intermediate',\n      institution: 'Narayana Junior College, State Board',\n      grade: 'CGPA: 5.55',\n    },\n    {\n      year: 'Graduated 2020',\n      degree: 'SSC',\n      institution: 'Narayana EM High School, State Board',\n      grade: 'CGPA: 9.88',\n    },\n  ];\n\n  const skillCategories = [\n    {\n      title: 'No-Code/Low-Code',\n      skills: ['SaaS development using AI-assisted tools & platforms'],\n    },\n    {\n      title: 'Cloud & Backend',\n      skills: [\n        'Supabase & Firebase – auth, DB, storage',\n        'API Integration & key management',\n        'Cost-optimized usage of 3rd-party services',\n      ],\n    },\n    {\n      title: 'Mobile Development',\n      skills: [\n        'Android & iOS dev via AI tools',\n        'Android Studio',\n        'Xcode',\n      ],\n    },\n    {\n      title: 'AI & Automation',\n      skills: [\n        'AI Prompt Engineering with low-iteration design',\n        'Workflow automation using n8n',\n        'Telegram bots for info delivery & engagement',\n      ],\n    },\n    {\n      title: 'Web Development',\n      skills: [\n        'HTML',\n        'CSS',\n        'JavaScript',\n        'Basic front-end tasks',\n      ],\n    },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n      },\n    },\n  };\n\n  return (\n    <section \n      id=\"about\" \n      className={cn(\n        'min-h-screen py-20 bg-muted/20 relative overflow-hidden',\n        className\n      )}\n    >\n      {/* Background decoration */}\n      <div className=\"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-primary/5 to-transparent\" />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl font-bold text-foreground mb-4\">\n              About Me\n            </h2>\n            <div className=\"w-20 h-1 bg-primary mx-auto rounded-full\" />\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16\">\n            {/* About Text */}\n            <motion.div variants={itemVariants} className=\"space-y-8\">\n              <div className=\"prose prose-lg dark:prose-invert max-w-none\">\n                <p className=\"text-lg text-muted-foreground leading-relaxed\">\n                  Recent Computer Science and Engineering graduate specializing in no-code/low-code development and AI-assisted tools. \n                  I focus on creating efficient digital solutions with minimal traditional coding, leveraging AI tools, cloud backends, \n                  and automation workflows. I'm seeking opportunities where I can apply my skills in AI prompt engineering, SaaS development, \n                  and mobile app creation to deliver cost-effective and innovative solutions.\n                </p>\n              </div>\n\n              {/* Education Section */}\n              <div className=\"education-container\">\n                <motion.h3 \n                  className=\"text-2xl font-semibold text-foreground mb-6 flex items-center gap-2\"\n                  variants={itemVariants}\n                >\n                  <GraduationCap className=\"w-6 h-6 text-primary\" />\n                  Education\n                </motion.h3>\n                \n                <div className=\"space-y-6\">\n                  {educationData.map((edu, index) => (\n                    <motion.div\n                      key={index}\n                      className=\"education-item flex gap-4 p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors\"\n                      variants={itemVariants}\n                      whileHover={{ scale: 1.02 }}\n                      transition={{ duration: 0.2 }}\n                    >\n                      <div className=\"education-year flex-shrink-0\">\n                        <div className=\"flex items-center gap-2 text-sm font-medium text-primary\">\n                          <Calendar className=\"w-4 h-4\" />\n                          {edu.year}\n                        </div>\n                      </div>\n                      <div className=\"education-details flex-1\">\n                        <h4 className=\"font-semibold text-foreground mb-1\">{edu.degree}</h4>\n                        <p className=\"text-muted-foreground mb-1\">{edu.institution}</p>\n                        <p className=\"text-sm text-muted-foreground\">{edu.grade}</p>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Skills Section */}\n            <motion.div variants={itemVariants} className=\"skills-container\">\n              <motion.h3 \n                className=\"text-2xl font-semibold text-foreground mb-6 flex items-center gap-2\"\n                variants={itemVariants}\n              >\n                <Award className=\"w-6 h-6 text-primary\" />\n                Skills\n              </motion.h3>\n              \n              <div className=\"skills-grid space-y-6\">\n                {skillCategories.map((category, index) => (\n                  <motion.div\n                    key={index}\n                    className=\"skill-category p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors\"\n                    variants={itemVariants}\n                    whileHover={{ scale: 1.02 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <h4 className=\"font-semibold text-foreground mb-3\">{category.title}</h4>\n                    <ul className=\"skills-list space-y-2\">\n                      {category.skills.map((skill, skillIndex) => (\n                        <li \n                          key={skillIndex}\n                          className=\"text-muted-foreground flex items-start gap-2\"\n                        >\n                          <span className=\"w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0\" />\n                          {skill}\n                        </li>\n                      ))}\n                    </ul>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AALA;;;;;AAWO,SAAS,aAAa,EAAE,SAAS,EAAqB;IAC3D,MAAM,gBAAgB;QACpB;YACE,MAAM;YACN,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,QAAQ;YACR,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,QAAQ;gBAAC;aAAuD;QAClE;QACA;YACE,OAAO;YACP,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;;0BAIF,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;;sCAGzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;sDAS/D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oDACR,WAAU;oDACV,UAAU;;sEAEV,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;8DAIpD,8OAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,KAAK,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,WAAU;4DACV,UAAU;4DACV,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,YAAY;gEAAE,UAAU;4DAAI;;8EAE5B,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EACnB,IAAI,IAAI;;;;;;;;;;;;8EAGb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAsC,IAAI,MAAM;;;;;;sFAC9D,8OAAC;4EAAE,WAAU;sFAA8B,IAAI,WAAW;;;;;;sFAC1D,8OAAC;4EAAE,WAAU;sFAAiC,IAAI,KAAK;;;;;;;;;;;;;2DAfpD;;;;;;;;;;;;;;;;;;;;;;8CAwBf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,UAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAI5C,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,UAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,YAAY;wDAAE,UAAU;oDAAI;;sEAE5B,8OAAC;4DAAG,WAAU;sEAAsC,SAAS,KAAK;;;;;;sEAClE,8OAAC;4DAAG,WAAU;sEACX,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC3B,8OAAC;oEAEC,WAAU;;sFAEV,8OAAC;4EAAK,WAAU;;;;;;wEACf;;mEAJI;;;;;;;;;;;mDAVN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BzB", "debugId": null}}, {"offset": {"line": 1447, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/interactive/macos-window.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useEffect } from 'react';\nimport { motion, PanInfo } from 'framer-motion';\nimport { X, Minus, Square } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface MacOSWindowProps {\n  id: string;\n  title: string;\n  children: React.ReactNode;\n  position: { x: number; y: number };\n  size: { width: number; height: number };\n  isMinimized: boolean;\n  isMaximized: boolean;\n  zIndex: number;\n  onClose: () => void;\n  onMinimize: () => void;\n  onMaximize: () => void;\n  onPositionChange: (position: { x: number; y: number }) => void;\n  onFocus: () => void;\n  className?: string;\n}\n\nexport function MacOSWindow({\n  id,\n  title,\n  children,\n  position,\n  size,\n  isMinimized,\n  isMaximized,\n  zIndex,\n  onClose,\n  onMinimize,\n  onMaximize,\n  onPositionChange,\n  onFocus,\n  className,\n}: MacOSWindowProps) {\n  const windowRef = useRef<HTMLDivElement>(null);\n  const isDraggingRef = useRef(false);\n\n  const handleDragStart = () => {\n    isDraggingRef.current = true;\n    onFocus();\n  };\n\n  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {\n    isDraggingRef.current = false;\n    const newX = Math.max(0, Math.min(position.x + info.offset.x, window.innerWidth - size.width));\n    const newY = Math.max(0, Math.min(position.y + info.offset.y, window.innerHeight - size.height));\n    onPositionChange({ x: newX, y: newY });\n  };\n\n  const handleWindowClick = () => {\n    if (!isDraggingRef.current) {\n      onFocus();\n    }\n  };\n\n  // Prevent window from going off-screen when window resizes\n  useEffect(() => {\n    const handleResize = () => {\n      if (windowRef.current) {\n        const maxX = window.innerWidth - size.width;\n        const maxY = window.innerHeight - size.height;\n        \n        if (position.x > maxX || position.y > maxY) {\n          onPositionChange({\n            x: Math.max(0, Math.min(position.x, maxX)),\n            y: Math.max(0, Math.min(position.y, maxY)),\n          });\n        }\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [position, size, onPositionChange]);\n\n  if (isMinimized) {\n    return null;\n  }\n\n  const windowVariants = {\n    initial: {\n      scale: 0.8,\n      opacity: 0,\n      x: position.x,\n      y: position.y,\n    },\n    animate: {\n      scale: 1,\n      opacity: 1,\n      x: position.x,\n      y: position.y,\n      transition: {\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n      },\n    },\n    exit: {\n      scale: 0.8,\n      opacity: 0,\n      transition: {\n        duration: 0.2,\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      ref={windowRef}\n      className={cn(\n        'absolute bg-background/95 backdrop-blur-md rounded-lg shadow-2xl border border-border/50 overflow-hidden',\n        'select-none',\n        className\n      )}\n      style={{\n        width: isMaximized ? '100vw' : size.width,\n        height: isMaximized ? 'calc(100vh - 100px)' : size.height,\n        zIndex,\n      }}\n      variants={windowVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      exit=\"exit\"\n      drag={!isMaximized}\n      dragMomentum={false}\n      dragElastic={0}\n      dragConstraints={{\n        left: 0,\n        right: window.innerWidth - size.width,\n        top: 0,\n        bottom: window.innerHeight - size.height,\n      }}\n      onDragStart={handleDragStart}\n      onDragEnd={handleDragEnd}\n      onClick={handleWindowClick}\n      whileHover={{ \n        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n      }}\n    >\n      {/* Window Header */}\n      <div className=\"flex items-center justify-between h-12 px-4 bg-muted/30 border-b border-border/50 cursor-move\">\n        {/* Traffic Light Buttons */}\n        <div className=\"flex items-center gap-2\">\n          <motion.button\n            className=\"w-3 h-3 bg-red-500 rounded-full hover:bg-red-600 transition-colors\"\n            onClick={(e) => {\n              e.stopPropagation();\n              onClose();\n            }}\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            aria-label=\"Close window\"\n          >\n            <X className=\"w-2 h-2 text-red-900 opacity-0 hover:opacity-100 transition-opacity mx-auto\" />\n          </motion.button>\n          \n          <motion.button\n            className=\"w-3 h-3 bg-yellow-500 rounded-full hover:bg-yellow-600 transition-colors\"\n            onClick={(e) => {\n              e.stopPropagation();\n              onMinimize();\n            }}\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            aria-label=\"Minimize window\"\n          >\n            <Minus className=\"w-2 h-2 text-yellow-900 opacity-0 hover:opacity-100 transition-opacity mx-auto\" />\n          </motion.button>\n          \n          <motion.button\n            className=\"w-3 h-3 bg-green-500 rounded-full hover:bg-green-600 transition-colors\"\n            onClick={(e) => {\n              e.stopPropagation();\n              onMaximize();\n            }}\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            aria-label=\"Maximize window\"\n          >\n            <Square className=\"w-2 h-2 text-green-900 opacity-0 hover:opacity-100 transition-opacity mx-auto\" />\n          </motion.button>\n        </div>\n\n        {/* Window Title */}\n        <div className=\"absolute left-1/2 transform -translate-x-1/2 pointer-events-none\">\n          <h3 className=\"text-sm font-medium text-foreground truncate max-w-48\">\n            {title}\n          </h3>\n        </div>\n\n        {/* Spacer for layout balance */}\n        <div className=\"w-16\" />\n      </div>\n\n      {/* Window Content */}\n      <div className=\"h-full overflow-hidden\" style={{ height: 'calc(100% - 48px)' }}>\n        {children}\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAwBO,SAAS,YAAY,EAC1B,EAAE,EACF,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,WAAW,EACX,MAAM,EACN,OAAO,EACP,UAAU,EACV,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,SAAS,EACQ;IACjB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,MAAM,kBAAkB;QACtB,cAAc,OAAO,GAAG;QACxB;IACF;IAEA,MAAM,gBAAgB,CAAC,OAA+C;QACpE,cAAc,OAAO,GAAG;QACxB,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,EAAE,OAAO,UAAU,GAAG,KAAK,KAAK;QAC5F,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,EAAE,OAAO,WAAW,GAAG,KAAK,MAAM;QAC9F,iBAAiB;YAAE,GAAG;YAAM,GAAG;QAAK;IACtC;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B;QACF;IACF;IAEA,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,UAAU,OAAO,EAAE;gBACrB,MAAM,OAAO,OAAO,UAAU,GAAG,KAAK,KAAK;gBAC3C,MAAM,OAAO,OAAO,WAAW,GAAG,KAAK,MAAM;gBAE7C,IAAI,SAAS,CAAC,GAAG,QAAQ,SAAS,CAAC,GAAG,MAAM;oBAC1C,iBAAiB;wBACf,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE;wBACpC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE;oBACtC;gBACF;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;QAAU;QAAM;KAAiB;IAErC,IAAI,aAAa;QACf,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,SAAS;YACP,OAAO;YACP,SAAS;YACT,GAAG,SAAS,CAAC;YACb,GAAG,SAAS,CAAC;QACf;QACA,SAAS;YACP,OAAO;YACP,SAAS;YACT,GAAG,SAAS,CAAC;YACb,GAAG,SAAS,CAAC;YACb,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,MAAM;YACJ,OAAO;YACP,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4GACA,eACA;QAEF,OAAO;YACL,OAAO,cAAc,UAAU,KAAK,KAAK;YACzC,QAAQ,cAAc,wBAAwB,KAAK,MAAM;YACzD;QACF;QACA,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,MAAK;QACL,MAAM,CAAC;QACP,cAAc;QACd,aAAa;QACb,iBAAiB;YACf,MAAM;YACN,OAAO,OAAO,UAAU,GAAG,KAAK,KAAK;YACrC,KAAK;YACL,QAAQ,OAAO,WAAW,GAAG,KAAK,MAAM;QAC1C;QACA,aAAa;QACb,WAAW;QACX,SAAS;QACT,YAAY;YACV,WAAW;QACb;;0BAGA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,cAAW;0CAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;0CAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,cAAW;0CAEX,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAGnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,cAAW;0CAEX,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX;;;;;;;;;;;kCAKL,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;gBAAyB,OAAO;oBAAE,QAAQ;gBAAoB;0BAC1E;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 1701, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/interactive/macos-dock.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Calculator, Github, Linkedin, Folder, Terminal, Mail } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface MacOSDockProps {\n  openWindows: Array<{ id: string; title: string; isMinimized: boolean }>;\n  onWindowRestore: (windowId: string) => void;\n  className?: string;\n}\n\nexport function MacOSDock({ openWindows, onWindowRestore, className }: MacOSDockProps) {\n  const [hoveredIcon, setHoveredIcon] = useState<string | null>(null);\n\n  const dockApps = [\n    {\n      id: 'finder',\n      name: 'Finder',\n      icon: Folder,\n      color: 'text-blue-500',\n      action: () => console.log('Finder clicked'),\n    },\n    {\n      id: 'calculator',\n      name: 'Calculator',\n      icon: Calculator,\n      color: 'text-gray-700',\n      action: () => window.open('https://calculator.net/', '_blank'),\n    },\n    {\n      id: 'terminal',\n      name: 'Terminal',\n      icon: Terminal,\n      color: 'text-black',\n      action: () => console.log('Terminal clicked'),\n    },\n    {\n      id: 'github',\n      name: 'GitHub',\n      icon: Github,\n      color: 'text-gray-900',\n      action: () => window.open('https://github.com/nrenx', '_blank'),\n    },\n    {\n      id: 'linkedin',\n      name: 'LinkedIn',\n      icon: Linkedin,\n      color: 'text-blue-600',\n      action: () => window.open('https://linkedin.com/in/bollineninarendrachowdary', '_blank'),\n    },\n    {\n      id: 'mail',\n      name: 'Mail',\n      icon: Mail,\n      color: 'text-blue-500',\n      action: () => window.open('mailto:<EMAIL>', '_blank'),\n    },\n  ];\n\n  const minimizedWindows = openWindows.filter(w => w.isMinimized);\n\n  const handleIconClick = (app: typeof dockApps[0]) => {\n    app.action();\n  };\n\n  const handleMinimizedWindowClick = (windowId: string) => {\n    onWindowRestore(windowId);\n  };\n\n  return (\n    <div className={cn('absolute bottom-4 left-1/2 transform -translate-x-1/2', className)}>\n      <motion.div\n        className=\"bg-white/10 backdrop-blur-md rounded-2xl px-3 py-2 border border-white/20 shadow-2xl\"\n        initial={{ y: 100, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ delay: 0.5, duration: 0.5 }}\n      >\n        <div className=\"flex items-end gap-1\">\n          {/* Regular Apps */}\n          {dockApps.map((app) => {\n            const IconComponent = app.icon;\n            const isHovered = hoveredIcon === app.id;\n            \n            return (\n              <motion.div\n                key={app.id}\n                className=\"relative flex flex-col items-center\"\n                onMouseEnter={() => setHoveredIcon(app.id)}\n                onMouseLeave={() => setHoveredIcon(null)}\n              >\n                {/* Tooltip */}\n                <AnimatePresence>\n                  {isHovered && (\n                    <motion.div\n                      className=\"absolute -top-12 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg whitespace-nowrap\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: 10 }}\n                      transition={{ duration: 0.2 }}\n                    >\n                      {app.name}\n                      <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800\" />\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                {/* Icon */}\n                <motion.button\n                  className={cn(\n                    'w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center',\n                    'hover:bg-white/30 transition-colors cursor-pointer',\n                    'backdrop-blur-sm border border-white/10'\n                  )}\n                  onClick={() => handleIconClick(app)}\n                  whileHover={{ \n                    scale: 1.2,\n                    y: -8,\n                  }}\n                  whileTap={{ scale: 0.95 }}\n                  transition={{ \n                    type: 'spring', \n                    stiffness: 400, \n                    damping: 25 \n                  }}\n                >\n                  <IconComponent className={cn('w-6 h-6', app.color)} />\n                </motion.button>\n              </motion.div>\n            );\n          })}\n\n          {/* Separator */}\n          {minimizedWindows.length > 0 && (\n            <div className=\"w-px h-8 bg-white/30 mx-1 self-center\" />\n          )}\n\n          {/* Minimized Windows */}\n          {minimizedWindows.map((window) => (\n            <motion.div\n              key={`minimized-${window.id}`}\n              className=\"relative flex flex-col items-center\"\n              onMouseEnter={() => setHoveredIcon(`minimized-${window.id}`)}\n              onMouseLeave={() => setHoveredIcon(null)}\n            >\n              {/* Tooltip */}\n              <AnimatePresence>\n                {hoveredIcon === `minimized-${window.id}` && (\n                  <motion.div\n                    className=\"absolute -top-12 px-2 py-1 bg-gray-800 text-white text-xs rounded shadow-lg whitespace-nowrap\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: 10 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    {window.title}\n                    <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800\" />\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              {/* Minimized Window Icon */}\n              <motion.button\n                className=\"w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors cursor-pointer backdrop-blur-sm border border-white/10 relative\"\n                onClick={() => handleMinimizedWindowClick(window.id)}\n                whileHover={{ \n                  scale: 1.2,\n                  y: -8,\n                }}\n                whileTap={{ scale: 0.95 }}\n                transition={{ \n                  type: 'spring', \n                  stiffness: 400, \n                  damping: 25 \n                }}\n                initial={{ scale: 0, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                exit={{ scale: 0, opacity: 0 }}\n              >\n                <Folder className=\"w-6 h-6 text-blue-500\" />\n                \n                {/* Active indicator */}\n                <div className=\"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full\" />\n              </motion.button>\n            </motion.div>\n          ))}\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAaO,SAAS,UAAU,EAAE,WAAW,EAAE,eAAe,EAAE,SAAS,EAAkB;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,8MAAA,CAAA,aAAU;YAChB,OAAO;YACP,QAAQ,IAAM,OAAO,IAAI,CAAC,2BAA2B;QACvD;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,QAAQ,IAAM,QAAQ,GAAG,CAAC;QAC5B;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,QAAQ,IAAM,OAAO,IAAI,CAAC,4BAA4B;QACxD;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,QAAQ,IAAM,OAAO,IAAI,CAAC,qDAAqD;QACjF;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,QAAQ,IAAM,OAAO,IAAI,CAAC,0CAA0C;QACtE;KACD;IAED,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW;IAE9D,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM;IACZ;IAEA,MAAM,6BAA6B,CAAC;QAClC,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yDAAyD;kBAC1E,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,GAAG;gBAAK,SAAS;YAAE;YAC9B,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,YAAY;gBAAE,OAAO;gBAAK,UAAU;YAAI;sBAExC,cAAA,8OAAC;gBAAI,WAAU;;oBAEZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,gBAAgB,IAAI,IAAI;wBAC9B,MAAM,YAAY,gBAAgB,IAAI,EAAE;wBAExC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,cAAc,IAAM,eAAe,IAAI,EAAE;4BACzC,cAAc,IAAM,eAAe;;8CAGnC,8OAAC,yLAAA,CAAA,kBAAe;8CACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;;4CAE3B,IAAI,IAAI;0DACT,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qEACA,sDACA;oCAEF,SAAS,IAAM,gBAAgB;oCAC/B,YAAY;wCACV,OAAO;wCACP,GAAG,CAAC;oCACN;oCACA,UAAU;wCAAE,OAAO;oCAAK;oCACxB,YAAY;wCACV,MAAM;wCACN,WAAW;wCACX,SAAS;oCACX;8CAEA,cAAA,8OAAC;wCAAc,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,IAAI,KAAK;;;;;;;;;;;;2BAxC9C,IAAI,EAAE;;;;;oBA4CjB;oBAGC,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;wBAAI,WAAU;;;;;;oBAIhB,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,cAAc,IAAM,eAAe,CAAC,UAAU,EAAE,QAAO,EAAE,EAAE;4BAC3D,cAAc,IAAM,eAAe;;8CAGnC,8OAAC,yLAAA,CAAA,kBAAe;8CACb,gBAAgB,CAAC,UAAU,EAAE,QAAO,EAAE,EAAE,kBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;;4CAE3B,QAAO,KAAK;0DACb,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,WAAU;oCACV,SAAS,IAAM,2BAA2B,QAAO,EAAE;oCACnD,YAAY;wCACV,OAAO;wCACP,GAAG,CAAC;oCACN;oCACA,UAAU;wCAAE,OAAO;oCAAK;oCACxB,YAAY;wCACV,MAAM;wCACN,WAAW;wCACX,SAAS;oCACX;oCACA,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,MAAM;wCAAE,OAAO;wCAAG,SAAS;oCAAE;;sDAE7B,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;2BA1CZ,CAAC,UAAU,EAAE,QAAO,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;AAkD3C", "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/data/projects.ts"], "sourcesContent": ["import { Project } from '@/lib/types';\n\nexport const projects: Project[] = [\n  {\n    id: 'saas-dashboard',\n    title: 'SaaS Analytics Dashboard',\n    description: 'AI-powered analytics dashboard built with no-code tools',\n    longDescription: 'A comprehensive SaaS analytics dashboard that leverages AI-assisted development and no-code platforms. Features real-time data visualization, user management, and automated reporting capabilities.',\n    technologies: ['Bubble.io', 'Supabase', 'Zapier', 'Chart.js', 'Stripe API'],\n    features: [\n      'Real-time analytics and reporting',\n      'User authentication and management',\n      'Payment integration with Stripe',\n      'Automated email notifications',\n      'Responsive design for all devices',\n      'AI-powered insights and recommendations'\n    ],\n    githubUrl: 'https://github.com/nrenx/saas-dashboard',\n    liveUrl: 'https://saas-dashboard-demo.vercel.app',\n    imageUrl: '/assets/images/projects/saas-dashboard.jpg',\n    category: 'web',\n    status: 'completed',\n    startDate: '2023-08',\n    endDate: '2023-11',\n  },\n  {\n    id: 'mobile-fitness-app',\n    title: 'AI Fitness Companion',\n    description: 'Mobile fitness app with AI workout recommendations',\n    longDescription: 'A mobile fitness application that uses AI to provide personalized workout recommendations and track user progress. Built using AI-assisted development tools and cloud backend services.',\n    technologies: ['FlutterFlow', 'Firebase', 'OpenAI API', 'Stripe', 'Google Fit API'],\n    features: [\n      'AI-powered workout recommendations',\n      'Progress tracking and analytics',\n      'Social features and challenges',\n      'Integration with wearable devices',\n      'Nutrition tracking and meal planning',\n      'Premium subscription model'\n    ],\n    githubUrl: 'https://github.com/nrenx/fitness-app',\n    liveUrl: 'https://play.google.com/store/apps/details?id=com.fitness.ai',\n    imageUrl: '/assets/images/projects/fitness-app.jpg',\n    category: 'mobile',\n    status: 'completed',\n    startDate: '2023-05',\n    endDate: '2023-09',\n  },\n  {\n    id: 'automation-workflow',\n    title: 'Business Process Automation',\n    description: 'n8n workflow automation for business processes',\n    longDescription: 'A comprehensive business process automation system built with n8n that streamlines operations, automates repetitive tasks, and integrates multiple business tools.',\n    technologies: ['n8n', 'Telegram Bot API', 'Google Sheets API', 'Slack API', 'Webhook'],\n    features: [\n      'Automated lead processing',\n      'Multi-channel notifications',\n      'Data synchronization between platforms',\n      'Custom Telegram bot for team updates',\n      'Scheduled report generation',\n      'Error handling and monitoring'\n    ],\n    githubUrl: 'https://github.com/nrenx/business-automation',\n    liveUrl: null,\n    imageUrl: '/assets/images/projects/automation.jpg',\n    category: 'other',\n    status: 'completed',\n    startDate: '2023-12',\n    endDate: '2024-02',\n  },\n  {\n    id: 'ecommerce-platform',\n    title: 'No-Code E-commerce Platform',\n    description: 'Complete e-commerce solution using no-code tools',\n    longDescription: 'A full-featured e-commerce platform built entirely with no-code tools, featuring product management, order processing, payment integration, and customer management.',\n    technologies: ['Webflow', 'Airtable', 'Zapier', 'Stripe', 'Mailchimp'],\n    features: [\n      'Product catalog management',\n      'Shopping cart and checkout',\n      'Payment processing with Stripe',\n      'Order management system',\n      'Customer relationship management',\n      'Email marketing automation'\n    ],\n    githubUrl: null,\n    liveUrl: 'https://ecommerce-demo.webflow.io',\n    imageUrl: '/assets/images/projects/ecommerce.jpg',\n    category: 'web',\n    status: 'completed',\n    startDate: '2023-03',\n    endDate: '2023-06',\n  },\n  {\n    id: 'ai-chatbot',\n    title: 'AI Customer Support Bot',\n    description: 'Intelligent chatbot for customer support automation',\n    longDescription: 'An AI-powered customer support chatbot that handles common inquiries, escalates complex issues, and provides 24/7 customer service using advanced prompt engineering.',\n    technologies: ['OpenAI GPT-4', 'Dialogflow', 'Firebase', 'Telegram API', 'Webhook'],\n    features: [\n      'Natural language understanding',\n      'Multi-language support',\n      'Integration with existing CRM',\n      'Escalation to human agents',\n      'Analytics and performance tracking',\n      'Custom training on business data'\n    ],\n    githubUrl: 'https://github.com/nrenx/ai-chatbot',\n    liveUrl: 'https://t.me/customer_support_ai_bot',\n    imageUrl: '/assets/images/projects/chatbot.jpg',\n    category: 'other',\n    status: 'completed',\n    startDate: '2024-01',\n    endDate: '2024-03',\n  },\n  {\n    id: 'portfolio-website',\n    title: 'Interactive Portfolio Website',\n    description: 'Modern portfolio with macOS-style interface',\n    longDescription: 'A modern, interactive portfolio website featuring a macOS-style interface, smooth animations, and responsive design. Built with React, Next.js, and Framer Motion.',\n    technologies: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS', 'Framer Motion'],\n    features: [\n      'macOS-style interface simulation',\n      'Multi-language landing animation',\n      'Dark/light theme switching',\n      'Smooth scroll animations',\n      'Responsive design',\n      'Contact form with validation'\n    ],\n    githubUrl: 'https://github.com/nrenx/portfolio',\n    liveUrl: 'https://narendrachowdary.dev',\n    imageUrl: '/assets/images/projects/portfolio.jpg',\n    category: 'web',\n    status: 'completed',\n    startDate: '2024-03',\n    endDate: '2024-05',\n  },\n];\n\n// Group projects by category\nexport const projectsByCategory = {\n  web: projects.filter(p => p.category === 'web'),\n  mobile: projects.filter(p => p.category === 'mobile'),\n  automation: projects.filter(p => p.category === 'other'),\n};\n\n// Get featured projects\nexport const featuredProjects = projects.slice(0, 3);\n\n// Get recent projects\nexport const recentProjects = projects\n  .sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())\n  .slice(0, 4);\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAa;YAAY;YAAU;YAAY;SAAa;QAC3E,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAe;YAAY;YAAc;YAAU;SAAiB;QACnF,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAO;YAAoB;YAAqB;YAAa;SAAU;QACtF,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAW;YAAY;YAAU;YAAU;SAAY;QACtE,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAgB;YAAc;YAAY;YAAgB;SAAU;QACnF,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,cAAc;YAAC;YAAS;YAAW;YAAc;YAAgB;SAAgB;QACjF,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;QACX,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;IACX;CACD;AAGM,MAAM,qBAAqB;IAChC,KAAK,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IACzC,QAAQ,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAC5C,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;AAClD;AAGO,MAAM,mBAAmB,SAAS,KAAK,CAAC,GAAG;AAG3C,MAAM,iBAAiB,SAC3B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,KAAK,CAAC,GAAG", "debugId": null}}, {"offset": {"line": 2189, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/interactive/macos-desktop.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Folder, X, Minus, Square } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { MacOSWindow } from './macos-window';\nimport { MacOSDock } from './macos-dock';\nimport { projectsByCategory } from '@/data/projects';\n\ninterface MacOSDesktopProps {\n  className?: string;\n}\n\ninterface OpenWindow {\n  id: string;\n  title: string;\n  content: React.ReactNode;\n  position: { x: number; y: number };\n  size: { width: number; height: number };\n  isMinimized: boolean;\n  isMaximized: boolean;\n  zIndex: number;\n}\n\nexport function MacOSDesktop({ className }: MacOSDesktopProps) {\n  const [openWindows, setOpenWindows] = useState<OpenWindow[]>([]);\n  const [nextZIndex, setNextZIndex] = useState(100);\n\n  const folders = [\n    {\n      id: 'web-apps',\n      name: 'Web Apps',\n      projects: projectsByCategory.web,\n      position: { x: 50, y: 50 },\n    },\n    {\n      id: 'mobile-apps',\n      name: 'Mobile Apps',\n      projects: projectsByCategory.mobile,\n      position: { x: 50, y: 150 },\n    },\n    {\n      id: 'automation',\n      name: 'Automation',\n      projects: projectsByCategory.automation,\n      position: { x: 50, y: 250 },\n    },\n  ];\n\n  const openFolder = useCallback((folder: typeof folders[0]) => {\n    const existingWindow = openWindows.find(w => w.id === folder.id);\n    if (existingWindow) {\n      // Bring to front\n      setOpenWindows(prev => \n        prev.map(w => \n          w.id === folder.id \n            ? { ...w, zIndex: nextZIndex, isMinimized: false }\n            : w\n        )\n      );\n      setNextZIndex(prev => prev + 1);\n      return;\n    }\n\n    const newWindow: OpenWindow = {\n      id: folder.id,\n      title: folder.name,\n      content: (\n        <div className=\"p-6 h-full overflow-auto\">\n          <h3 className=\"text-lg font-semibold mb-4\">{folder.name}</h3>\n          <div className=\"grid gap-4\">\n            {folder.projects.map((project) => (\n              <motion.div\n                key={project.id}\n                className=\"p-4 bg-muted/50 rounded-lg border border-border/50 hover:border-primary/30 transition-colors cursor-pointer\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                onClick={() => openProjectWindow(project)}\n              >\n                <h4 className=\"font-medium text-foreground mb-2\">{project.title}</h4>\n                <p className=\"text-sm text-muted-foreground mb-3\">{project.description}</p>\n                <div className=\"flex flex-wrap gap-1\">\n                  {project.technologies.slice(0, 3).map((tech) => (\n                    <span\n                      key={tech}\n                      className=\"px-2 py-1 bg-primary/10 text-primary text-xs rounded-md\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                  {project.technologies.length > 3 && (\n                    <span className=\"px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md\">\n                      +{project.technologies.length - 3} more\n                    </span>\n                  )}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      ),\n      position: { x: 200 + Math.random() * 100, y: 100 + Math.random() * 50 },\n      size: { width: 500, height: 400 },\n      isMinimized: false,\n      isMaximized: false,\n      zIndex: nextZIndex,\n    };\n\n    setOpenWindows(prev => [...prev, newWindow]);\n    setNextZIndex(prev => prev + 1);\n  }, [openWindows, nextZIndex]);\n\n  const openProjectWindow = useCallback((project: any) => {\n    const existingWindow = openWindows.find(w => w.id === project.id);\n    if (existingWindow) {\n      setOpenWindows(prev => \n        prev.map(w => \n          w.id === project.id \n            ? { ...w, zIndex: nextZIndex, isMinimized: false }\n            : w\n        )\n      );\n      setNextZIndex(prev => prev + 1);\n      return;\n    }\n\n    const newWindow: OpenWindow = {\n      id: project.id,\n      title: project.title,\n      content: (\n        <div className=\"p-6 h-full overflow-auto\">\n          <div className=\"mb-4\">\n            <h3 className=\"text-xl font-bold text-foreground mb-2\">{project.title}</h3>\n            <p className=\"text-muted-foreground\">{project.longDescription}</p>\n          </div>\n          \n          <div className=\"mb-4\">\n            <h4 className=\"font-semibold text-foreground mb-2\">Technologies</h4>\n            <div className=\"flex flex-wrap gap-2\">\n              {project.technologies.map((tech: string) => (\n                <span\n                  key={tech}\n                  className=\"px-3 py-1 bg-primary/10 text-primary text-sm rounded-md\"\n                >\n                  {tech}\n                </span>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"mb-4\">\n            <h4 className=\"font-semibold text-foreground mb-2\">Key Features</h4>\n            <ul className=\"space-y-1\">\n              {project.features.map((feature: string, index: number) => (\n                <li key={index} className=\"text-sm text-muted-foreground flex items-start gap-2\">\n                  <span className=\"w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0\" />\n                  {feature}\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          <div className=\"flex gap-3 pt-4 border-t border-border/50\">\n            {project.githubUrl && (\n              <a\n                href={project.githubUrl}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:bg-primary/90 transition-colors\"\n              >\n                View Code\n              </a>\n            )}\n            {project.liveUrl && (\n              <a\n                href={project.liveUrl}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"px-4 py-2 border border-border text-foreground rounded-lg text-sm font-medium hover:bg-muted/50 transition-colors\"\n              >\n                Live Demo\n              </a>\n            )}\n          </div>\n        </div>\n      ),\n      position: { x: 250 + Math.random() * 100, y: 150 + Math.random() * 50 },\n      size: { width: 600, height: 500 },\n      isMinimized: false,\n      isMaximized: false,\n      zIndex: nextZIndex,\n    };\n\n    setOpenWindows(prev => [...prev, newWindow]);\n    setNextZIndex(prev => prev + 1);\n  }, [openWindows, nextZIndex]);\n\n  const closeWindow = useCallback((windowId: string) => {\n    setOpenWindows(prev => prev.filter(w => w.id !== windowId));\n  }, []);\n\n  const minimizeWindow = useCallback((windowId: string) => {\n    setOpenWindows(prev => \n      prev.map(w => \n        w.id === windowId ? { ...w, isMinimized: true } : w\n      )\n    );\n  }, []);\n\n  const maximizeWindow = useCallback((windowId: string) => {\n    setOpenWindows(prev => \n      prev.map(w => \n        w.id === windowId \n          ? { \n              ...w, \n              isMaximized: !w.isMaximized,\n              position: w.isMaximized ? w.position : { x: 0, y: 0 },\n              size: w.isMaximized ? w.size : { width: window.innerWidth, height: window.innerHeight - 100 }\n            }\n          : w\n      )\n    );\n  }, []);\n\n  const updateWindowPosition = useCallback((windowId: string, position: { x: number; y: number }) => {\n    setOpenWindows(prev => \n      prev.map(w => \n        w.id === windowId ? { ...w, position } : w\n      )\n    );\n  }, []);\n\n  const bringToFront = useCallback((windowId: string) => {\n    setOpenWindows(prev => \n      prev.map(w => \n        w.id === windowId ? { ...w, zIndex: nextZIndex } : w\n      )\n    );\n    setNextZIndex(prev => prev + 1);\n  }, [nextZIndex]);\n\n  return (\n    <div className={cn('relative w-full h-full overflow-hidden', className)}>\n      {/* Desktop Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-300/20 via-transparent to-purple-500/20\" />\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1)_0%,transparent_50%)]\" />\n      </div>\n\n      {/* Desktop Icons */}\n      <div className=\"absolute inset-0 p-8\">\n        {folders.map((folder) => (\n          <motion.div\n            key={folder.id}\n            className=\"absolute flex flex-col items-center cursor-pointer group\"\n            style={{ left: folder.position.x, top: folder.position.y }}\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            onDoubleClick={() => openFolder(folder)}\n          >\n            <div className=\"w-16 h-16 mb-2 relative\">\n              <Folder className=\"w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-colors\" />\n              <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg\" />\n            </div>\n            <span className=\"text-white text-sm font-medium text-center drop-shadow-md max-w-20 leading-tight\">\n              {folder.name}\n            </span>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Windows */}\n      <AnimatePresence>\n        {openWindows.map((window) => (\n          <MacOSWindow\n            key={window.id}\n            id={window.id}\n            title={window.title}\n            position={window.position}\n            size={window.size}\n            isMinimized={window.isMinimized}\n            isMaximized={window.isMaximized}\n            zIndex={window.zIndex}\n            onClose={() => closeWindow(window.id)}\n            onMinimize={() => minimizeWindow(window.id)}\n            onMaximize={() => maximizeWindow(window.id)}\n            onPositionChange={(position) => updateWindowPosition(window.id, position)}\n            onFocus={() => bringToFront(window.id)}\n          >\n            {window.content}\n          </MacOSWindow>\n        ))}\n      </AnimatePresence>\n\n      {/* Dock */}\n      <MacOSDock \n        openWindows={openWindows}\n        onWindowRestore={(windowId) => {\n          setOpenWindows(prev => \n            prev.map(w => \n              w.id === windowId \n                ? { ...w, isMinimized: false, zIndex: nextZIndex }\n                : w\n            )\n          );\n          setNextZIndex(prev => prev + 1);\n        }}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAyBO,SAAS,aAAa,EAAE,SAAS,EAAqB;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,UAAU;QACd;YACE,IAAI;YACJ,MAAM;YACN,UAAU,uHAAA,CAAA,qBAAkB,CAAC,GAAG;YAChC,UAAU;gBAAE,GAAG;gBAAI,GAAG;YAAG;QAC3B;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU,uHAAA,CAAA,qBAAkB,CAAC,MAAM;YACnC,UAAU;gBAAE,GAAG;gBAAI,GAAG;YAAI;QAC5B;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU,uHAAA,CAAA,qBAAkB,CAAC,UAAU;YACvC,UAAU;gBAAE,GAAG;gBAAI,GAAG;YAAI;QAC5B;KACD;IAED,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,MAAM,iBAAiB,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;QAC/D,IAAI,gBAAgB;YAClB,iBAAiB;YACjB,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,OAAO,EAAE,GACd;wBAAE,GAAG,CAAC;wBAAE,QAAQ;wBAAY,aAAa;oBAAM,IAC/C;YAGR,cAAc,CAAA,OAAQ,OAAO;YAC7B;QACF;QAEA,MAAM,YAAwB;YAC5B,IAAI,OAAO,EAAE;YACb,OAAO,OAAO,IAAI;YAClB,uBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8B,OAAO,IAAI;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,kBAAkB;;kDAEjC,8OAAC;wCAAG,WAAU;kDAAoC,QAAQ,KAAK;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;kDAAsC,QAAQ,WAAW;;;;;;kDACtE,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACrC,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;4CAMR,QAAQ,YAAY,CAAC,MAAM,GAAG,mBAC7B,8OAAC;gDAAK,WAAU;;oDAA8D;oDAC1E,QAAQ,YAAY,CAAC,MAAM,GAAG;oDAAE;;;;;;;;;;;;;;+BAnBnC,QAAQ,EAAE;;;;;;;;;;;;;;;;YA4BzB,UAAU;gBAAE,GAAG,MAAM,KAAK,MAAM,KAAK;gBAAK,GAAG,MAAM,KAAK,MAAM,KAAK;YAAG;YACtE,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAI;YAChC,aAAa;YACb,aAAa;YACb,QAAQ;QACV;QAEA,eAAe,CAAA,OAAQ;mBAAI;gBAAM;aAAU;QAC3C,cAAc,CAAA,OAAQ,OAAO;IAC/B,GAAG;QAAC;QAAa;KAAW;IAE5B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,MAAM,iBAAiB,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE;QAChE,IAAI,gBAAgB;YAClB,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,QAAQ,EAAE,GACf;wBAAE,GAAG,CAAC;wBAAE,QAAQ;wBAAY,aAAa;oBAAM,IAC/C;YAGR,cAAc,CAAA,OAAQ,OAAO;YAC7B;QACF;QAEA,MAAM,YAAwB;YAC5B,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,KAAK;YACpB,uBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0C,QAAQ,KAAK;;;;;;0CACrE,8OAAC;gCAAE,WAAU;0CAAyB,QAAQ,eAAe;;;;;;;;;;;;kCAG/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;;;;;;kCASb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAG,WAAU;0CACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAiB,sBACtC,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAK,WAAU;;;;;;4CACf;;uCAFM;;;;;;;;;;;;;;;;kCAQf,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,SAAS,kBAChB,8OAAC;gCACC,MAAM,QAAQ,SAAS;gCACvB,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;4BAIF,QAAQ,OAAO,kBACd,8OAAC;gCACC,MAAM,QAAQ,OAAO;gCACrB,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAOT,UAAU;gBAAE,GAAG,MAAM,KAAK,MAAM,KAAK;gBAAK,GAAG,MAAM,KAAK,MAAM,KAAK;YAAG;YACtE,MAAM;gBAAE,OAAO;gBAAK,QAAQ;YAAI;YAChC,aAAa;YACb,aAAa;YACb,QAAQ;QACV;QAEA,eAAe,CAAA,OAAQ;mBAAI;gBAAM;aAAU;QAC3C,cAAc,CAAA,OAAQ,OAAO;IAC/B,GAAG;QAAC;QAAa;KAAW;IAE5B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACnD,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,WAAW;oBAAE,GAAG,CAAC;oBAAE,aAAa;gBAAK,IAAI;IAGxD,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,WACL;oBACE,GAAG,CAAC;oBACJ,aAAa,CAAC,EAAE,WAAW;oBAC3B,UAAU,EAAE,WAAW,GAAG,EAAE,QAAQ,GAAG;wBAAE,GAAG;wBAAG,GAAG;oBAAE;oBACpD,MAAM,EAAE,WAAW,GAAG,EAAE,IAAI,GAAG;wBAAE,OAAO,OAAO,UAAU;wBAAE,QAAQ,OAAO,WAAW,GAAG;oBAAI;gBAC9F,IACA;IAGV,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,UAAkB;QAC1D,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,WAAW;oBAAE,GAAG,CAAC;oBAAE;gBAAS,IAAI;IAG/C,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,WAAW;oBAAE,GAAG,CAAC;oBAAE,QAAQ;gBAAW,IAAI;QAGvD,cAAc,CAAA,OAAQ,OAAO;IAC/B,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;0BAE3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,OAAO;4BAAE,MAAM,OAAO,QAAQ,CAAC,CAAC;4BAAE,KAAK,OAAO,QAAQ,CAAC,CAAC;wBAAC;wBACzD,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,eAAe,IAAM,WAAW;;0CAEhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,8OAAC;gCAAK,WAAU;0CACb,OAAO,IAAI;;;;;;;uBAZT,OAAO,EAAE;;;;;;;;;;0BAmBpB,8OAAC,yLAAA,CAAA,kBAAe;0BACb,YAAY,GAAG,CAAC,CAAC,wBAChB,8OAAC,oJAAA,CAAA,cAAW;wBAEV,IAAI,QAAO,EAAE;wBACb,OAAO,QAAO,KAAK;wBACnB,UAAU,QAAO,QAAQ;wBACzB,MAAM,QAAO,IAAI;wBACjB,aAAa,QAAO,WAAW;wBAC/B,aAAa,QAAO,WAAW;wBAC/B,QAAQ,QAAO,MAAM;wBACrB,SAAS,IAAM,YAAY,QAAO,EAAE;wBACpC,YAAY,IAAM,eAAe,QAAO,EAAE;wBAC1C,YAAY,IAAM,eAAe,QAAO,EAAE;wBAC1C,kBAAkB,CAAC,WAAa,qBAAqB,QAAO,EAAE,EAAE;wBAChE,SAAS,IAAM,aAAa,QAAO,EAAE;kCAEpC,QAAO,OAAO;uBAdV,QAAO,EAAE;;;;;;;;;;0BAoBpB,8OAAC,kJAAA,CAAA,YAAS;gBACR,aAAa;gBACb,iBAAiB,CAAC;oBAChB,eAAe,CAAA,OACb,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,KAAK,WACL;gCAAE,GAAG,CAAC;gCAAE,aAAa;gCAAO,QAAQ;4BAAW,IAC/C;oBAGR,cAAc,CAAA,OAAQ,OAAO;gBAC/B;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2711, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/sections/projects-section.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Folder, Monitor, Play } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { MacOSDesktop } from '@/components/interactive/macos-desktop';\n\ninterface ProjectsSectionProps {\n  className?: string;\n}\n\nexport function ProjectsSection({ className }: ProjectsSectionProps) {\n  const [showMacOS, setShowMacOS] = useState(false);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n      },\n    },\n  };\n\n  return (\n    <section\n      id=\"projects\"\n      className={cn(\n        'min-h-screen py-20 bg-muted/20 relative overflow-hidden',\n        className\n      )}\n    >\n      {/* Background decoration */}\n      <div className=\"absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-primary/5 to-transparent\" />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl font-bold text-foreground mb-4\">\n              Projects\n            </h2>\n            <div className=\"w-20 h-1 bg-primary mx-auto rounded-full mb-6\" />\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Explore my projects through an interactive macOS-style interface. Click on folders to discover different categories of work.\n            </p>\n          </motion.div>\n\n          {/* macOS Interface Container */}\n          <motion.div\n            variants={itemVariants}\n            className=\"macos-interface-container\"\n          >\n            {!showMacOS ? (\n              /* Preview Mode */\n              <div className=\"relative w-full h-[600px] lg:h-[700px] bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl overflow-hidden shadow-2xl\">\n                {/* macOS Wallpaper Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-br from-blue-300/20 via-transparent to-purple-500/20\" />\n                <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1)_0%,transparent_50%)]\" />\n\n                {/* Desktop Content */}\n                <div className=\"relative h-full p-8\">\n                  {/* Project Folders */}\n                  <div className=\"grid grid-cols-4 lg:grid-cols-6 gap-6 h-full\">\n                    <motion.div\n                      className=\"flex flex-col items-center cursor-pointer group\"\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                    >\n                      <div className=\"w-16 h-16 lg:w-20 lg:h-20 mb-2 relative\">\n                        <Folder className=\"w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-colors\" />\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg\" />\n                      </div>\n                      <span className=\"text-white text-sm font-medium text-center drop-shadow-md\">\n                        Web Apps\n                      </span>\n                    </motion.div>\n\n                    <motion.div\n                      className=\"flex flex-col items-center cursor-pointer group\"\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                    >\n                      <div className=\"w-16 h-16 lg:w-20 lg:h-20 mb-2 relative\">\n                        <Folder className=\"w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-colors\" />\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg\" />\n                      </div>\n                      <span className=\"text-white text-sm font-medium text-center drop-shadow-md\">\n                        Mobile Apps\n                      </span>\n                    </motion.div>\n\n                    <motion.div\n                      className=\"flex flex-col items-center cursor-pointer group\"\n                      whileHover={{ scale: 1.05 }}\n                      whileTap={{ scale: 0.95 }}\n                    >\n                      <div className=\"w-16 h-16 lg:w-20 lg:h-20 mb-2 relative\">\n                        <Folder className=\"w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-colors\" />\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg\" />\n                      </div>\n                      <span className=\"text-white text-sm font-medium text-center drop-shadow-md\">\n                        Automation\n                      </span>\n                    </motion.div>\n                  </div>\n\n                  {/* Interactive Launch Overlay */}\n                  <div className=\"absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center\">\n                    <motion.div\n                      className=\"text-center text-white\"\n                      initial={{ opacity: 0, scale: 0.9 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      transition={{ delay: 0.5 }}\n                    >\n                      <Monitor className=\"w-16 h-16 mx-auto mb-4 opacity-80\" />\n                      <h3 className=\"text-2xl font-bold mb-2\">Interactive macOS Interface</h3>\n                      <p className=\"text-lg opacity-90 mb-6\">Explore my projects through a realistic macOS desktop experience</p>\n\n                      <motion.button\n                        onClick={() => setShowMacOS(true)}\n                        className=\"px-8 py-3 bg-white/20 backdrop-blur-md rounded-lg border border-white/30 text-white font-medium hover:bg-white/30 transition-all duration-300 flex items-center gap-2 mx-auto\"\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <Play className=\"w-5 h-5\" />\n                        Launch macOS Interface\n                      </motion.button>\n                    </motion.div>\n                  </div>\n                </div>\n\n                {/* macOS Dock Placeholder */}\n                <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2\">\n                  <div className=\"bg-white/10 backdrop-blur-md rounded-2xl px-4 py-2 border border-white/20\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-8 h-8 bg-white/20 rounded-lg\" />\n                      <div className=\"w-8 h-8 bg-white/20 rounded-lg\" />\n                      <div className=\"w-8 h-8 bg-white/20 rounded-lg\" />\n                      <div className=\"w-1 h-6 bg-white/30 rounded-full mx-1\" />\n                      <div className=\"w-8 h-8 bg-white/20 rounded-lg\" />\n                      <div className=\"w-8 h-8 bg-white/20 rounded-lg\" />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              /* Interactive macOS Desktop */\n              <div className=\"relative w-full h-[600px] lg:h-[700px] rounded-2xl overflow-hidden shadow-2xl\">\n                <MacOSDesktop />\n\n                {/* Exit Button */}\n                <motion.button\n                  onClick={() => setShowMacOS(false)}\n                  className=\"absolute top-4 right-4 z-[1000] px-4 py-2 bg-black/50 backdrop-blur-md rounded-lg border border-white/20 text-white text-sm font-medium hover:bg-black/70 transition-all duration-300\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Exit macOS\n                </motion.button>\n              </div>\n            )}\n          </motion.div>\n\n          {/* Project Stats */}\n          <motion.div\n            variants={itemVariants}\n            className=\"grid grid-cols-2 lg:grid-cols-4 gap-6 mt-12\"\n          >\n            {[\n              { label: 'Projects Completed', value: '10+' },\n              { label: 'Technologies Used', value: '15+' },\n              { label: 'Client Satisfaction', value: '100%' },\n              { label: 'Years Experience', value: '2+' },\n            ].map((stat, index) => (\n              <motion.div\n                key={index}\n                className=\"text-center p-6 bg-card/50 border border-border/50 rounded-lg\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.2 }}\n              >\n                <div className=\"text-2xl lg:text-3xl font-bold text-primary mb-2\">\n                  {stat.value}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,gBAAgB,EAAE,SAAS,EAAwB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;;0BAIF,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;;sCAGzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAMjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET,CAAC,YACA,gBAAgB,iBAChB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDAGf,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;0EAExB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;wEAAI,WAAU;;;;;;;;;;;;0EAEjB,8OAAC;gEAAK,WAAU;0EAA4D;;;;;;;;;;;;kEAK9E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;0EAExB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;wEAAI,WAAU;;;;;;;;;;;;0EAEjB,8OAAC;gEAAK,WAAU;0EAA4D;;;;;;;;;;;;kEAK9E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;;0EAExB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC;wEAAI,WAAU;;;;;;;;;;;;0EAEjB,8OAAC;gEAAK,WAAU;0EAA4D;;;;;;;;;;;;;;;;;;0DAOhF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,YAAY;wDAAE,OAAO;oDAAI;;sEAEzB,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAG,WAAU;sEAA0B;;;;;;sEACxC,8OAAC;4DAAE,WAAU;sEAA0B;;;;;;sEAEvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,SAAS,IAAM,aAAa;4DAC5B,WAAU;4DACV,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;;8EAExB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;;;;;;;;;;;;kDAQpC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAMvB,6BAA6B,iBAC7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qJAAA,CAAA,eAAY;;;;;kDAGb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS,IAAM,aAAa;wCAC5B,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDACzB;;;;;;;;;;;;;;;;;sCAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET;gCACC;oCAAE,OAAO;oCAAsB,OAAO;gCAAM;gCAC5C;oCAAE,OAAO;oCAAqB,OAAO;gCAAM;gCAC3C;oCAAE,OAAO;oCAAuB,OAAO;gCAAO;gCAC9C;oCAAE,OAAO;oCAAoB,OAAO;gCAAK;6BAC1C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCATR;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBrB", "debugId": null}}, {"offset": {"line": 3244, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/sections/experience-section.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Award, Code, Users, HandHeart, Laptop } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface ExperienceSectionProps {\n  className?: string;\n}\n\nexport function ExperienceSection({ className }: ExperienceSectionProps) {\n  const certifications = [\n    {\n      title: 'Critical Thinking & Problem Solving',\n      provider: 'LinkedIn Learning',\n      year: '2023',\n      icon: Award,\n    },\n    {\n      title: 'Python Programming',\n      provider: 'Coursera',\n      year: '2022',\n      icon: Code,\n    },\n    {\n      title: 'Web Development Fundamentals',\n      provider: 'Udemy',\n      year: '2023',\n      icon: Code,\n    },\n  ];\n\n  const activities = [\n    {\n      title: 'Participated in college-level coding competitions (2022-2023)',\n      icon: Laptop,\n    },\n    {\n      title: 'Member of the Computer Science Club at NBKRIST College',\n      icon: Users,\n    },\n    {\n      title: 'Volunteer for technical events at department symposiums',\n      icon: HandHeart,\n    },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n      },\n    },\n  };\n\n  const cardVariants = {\n    hidden: { opacity: 0, scale: 0.9 },\n    visible: {\n      opacity: 1,\n      scale: 1,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  return (\n    <section\n      id=\"experience\"\n      className={cn(\n        'min-h-screen py-20 bg-background relative overflow-hidden',\n        className\n      )}\n    >\n      {/* Background decoration */}\n      <div className=\"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-primary/3 to-transparent\" />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl font-bold text-foreground mb-4\">\n              Experience & Certifications\n            </h2>\n            <div className=\"w-20 h-1 bg-primary mx-auto rounded-full\" />\n          </motion.div>\n\n          <div className=\"space-y-16\">\n            {/* Certifications Section */}\n            <motion.div variants={itemVariants}>\n              <h3 className=\"text-2xl font-semibold text-foreground mb-8 flex items-center gap-2\">\n                <Award className=\"w-6 h-6 text-primary\" />\n                Certifications\n              </h3>\n\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {certifications.map((cert, index) => {\n                  const IconComponent = cert.icon;\n                  return (\n                    <motion.div\n                      key={index}\n                      className=\"certification-card group\"\n                      variants={cardVariants}\n                      whileHover={{\n                        scale: 1.05,\n                        transition: { duration: 0.2 }\n                      }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <div className=\"bg-card/50 border border-border/50 rounded-lg p-6 h-full transition-all duration-300 hover:border-primary/30 hover:shadow-lg\">\n                        <div className=\"flex items-start gap-4\">\n                          <div className=\"certification-icon flex-shrink-0\">\n                            <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors\">\n                              <IconComponent className=\"w-6 h-6 text-primary\" />\n                            </div>\n                          </div>\n                          <div className=\"certification-details flex-1\">\n                            <h4 className=\"font-semibold text-foreground mb-2 leading-tight\">\n                              {cert.title}\n                            </h4>\n                            <p className=\"text-muted-foreground text-sm\">\n                              {cert.provider} ({cert.year})\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n\n            {/* Extracurricular Activities Section */}\n            <motion.div variants={itemVariants}>\n              <h3 className=\"text-2xl font-semibold text-foreground mb-8 flex items-center gap-2\">\n                <Users className=\"w-6 h-6 text-primary\" />\n                Extracurricular Activities\n              </h3>\n\n              <div className=\"space-y-4\">\n                {activities.map((activity, index) => {\n                  const IconComponent = activity.icon;\n                  return (\n                    <motion.div\n                      key={index}\n                      className=\"activity-item group\"\n                      variants={cardVariants}\n                      whileHover={{\n                        x: 4,\n                        transition: { duration: 0.2 }\n                      }}\n                    >\n                      <div className=\"flex items-center gap-4 p-4 rounded-lg bg-card/30 border border-border/30 hover:border-primary/30 hover:bg-card/50 transition-all duration-300\">\n                        <div className=\"activity-icon flex-shrink-0\">\n                          <div className=\"w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors\">\n                            <IconComponent className=\"w-5 h-5 text-primary\" />\n                          </div>\n                        </div>\n                        <span className=\"text-foreground group-hover:text-foreground/90 transition-colors\">\n                          {activity.title}\n                        </span>\n                      </div>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;AAWO,SAAS,kBAAkB,EAAE,SAAS,EAA0B;IACrE,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,UAAU;YACV,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;QACb;QACA;YACE,OAAO;YACP,UAAU;YACV,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,OAAO;YACP,UAAU;YACV,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;KACD;IAED,MAAM,aAAa;QACjB;YACE,OAAO;YACP,MAAM,sMAAA,CAAA,SAAM;QACd;QACA;YACE,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;QACb;QACA;YACE,OAAO;YACP,MAAM,gNAAA,CAAA,YAAS;QACjB;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,OAAO;QAAI;QACjC,SAAS;YACP,SAAS;YACT,OAAO;YACP,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6DACA;;0BAIF,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;;sCAGzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;;sDACpB,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAI5C,8OAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,MAAM;gDACzB,MAAM,gBAAgB,KAAK,IAAI;gDAC/B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,UAAU;oDACV,YAAY;wDACV,OAAO;wDACP,YAAY;4DAAE,UAAU;wDAAI;oDAC9B;oDACA,UAAU;wDAAE,OAAO;oDAAK;8DAExB,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAc,WAAU;;;;;;;;;;;;;;;;8EAG7B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFACX,KAAK,KAAK;;;;;;sFAEb,8OAAC;4EAAE,WAAU;;gFACV,KAAK,QAAQ;gFAAC;gFAAG,KAAK,IAAI;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;mDArB/B;;;;;4CA4BX;;;;;;;;;;;;8CAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;;sDACpB,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAI5C,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,UAAU;gDACzB,MAAM,gBAAgB,SAAS,IAAI;gDACnC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,UAAU;oDACV,YAAY;wDACV,GAAG;wDACH,YAAY;4DAAE,UAAU;wDAAI;oDAC9B;8DAEA,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAc,WAAU;;;;;;;;;;;;;;;;0EAG7B,8OAAC;gEAAK,WAAU;0EACb,SAAS,KAAK;;;;;;;;;;;;mDAfd;;;;;4CAoBX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 3620, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/sections/contact-section.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Mail, Phone, MapPin, Send, FileText, Github, Linkedin, Twitter } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { FORM_CONFIG } from '@/lib/constants';\n\ninterface ContactSectionProps {\n  className?: string;\n}\n\ninterface FormData {\n  name: string;\n  email: string;\n  subject: string;\n  message: string;\n}\n\ninterface FormErrors {\n  name?: string;\n  email?: string;\n  subject?: string;\n  message?: string;\n}\n\nexport function ContactSection({ className }: ContactSectionProps) {\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    email: '',\n    subject: '',\n    message: '',\n  });\n  const [errors, setErrors] = useState<FormErrors>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n\n  const handleResumeClick = () => {\n    // Open the resume in a new window for preview\n    window.open('/assets/resume/resume.pdf', '_blank');\n  };\n\n  const contactInfo = [\n    {\n      icon: Mail,\n      label: 'Email',\n      value: '<EMAIL>',\n      href: 'mailto:<EMAIL>',\n    },\n    {\n      icon: Phone,\n      label: 'Phone',\n      value: '+91 ************',\n      href: 'tel:+917989976214',\n    },\n    {\n      icon: MapPin,\n      label: 'Location',\n      value: 'Edulapalli(Vi), Gudur(M), Tirupathi(D), Andhra Pradesh, 524409',\n      href: null,\n    },\n  ];\n\n  const socialLinks = [\n    {\n      icon: Github,\n      label: 'GitHub',\n      href: 'https://github.com/nrenx',\n    },\n    {\n      icon: Linkedin,\n      label: 'LinkedIn',\n      href: 'https://linkedin.com/in/bollineninarendrachowdary',\n    },\n    {\n      icon: Twitter,\n      label: 'Twitter',\n      href: '#',\n    },\n  ];\n\n  const validateForm = (): boolean => {\n    const newErrors: FormErrors = {};\n\n    // Name validation\n    if (!formData.name.trim()) {\n      newErrors.name = 'Name is required';\n    } else if (formData.name.length < FORM_CONFIG.validation.minNameLength) {\n      newErrors.name = `Name must be at least ${FORM_CONFIG.validation.minNameLength} characters`;\n    }\n\n    // Email validation\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!FORM_CONFIG.validation.email.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Subject validation\n    if (!formData.subject.trim()) {\n      newErrors.subject = 'Subject is required';\n    }\n\n    // Message validation\n    if (!formData.message.trim()) {\n      newErrors.message = 'Message is required';\n    } else if (formData.message.length < FORM_CONFIG.validation.minMessageLength) {\n      newErrors.message = `Message must be at least ${FORM_CONFIG.validation.minMessageLength} characters`;\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Clear error when user starts typing\n    if (errors[name as keyof FormErrors]) {\n      setErrors(prev => ({ ...prev, [name]: undefined }));\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) return;\n\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      // Simulate form submission\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      setSubmitStatus('success');\n      setFormData({ name: '', email: '', subject: '', message: '' });\n    } catch (error) {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n      },\n    },\n  };\n\n  return (\n    <section\n      id=\"contact\"\n      className={cn(\n        'min-h-screen py-20 pb-32 bg-background relative overflow-hidden',\n        className\n      )}\n    >\n      {/* Background decoration */}\n      <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-primary/5 via-transparent to-transparent\" />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h1 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              <span className=\"bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent uppercase tracking-wide\">\n                Got a Vision? Let's Bring It to Life!\n              </span>\n            </h1>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed\">\n              I'm enthusiastic about collaborating on innovative projects. Let's connect and explore how we can bring your vision to life!\n            </p>\n          </motion.div>\n\n          {/* Contact Content */}\n          <div className=\"grid lg:grid-cols-5 gap-12\">\n            {/* Contact Info */}\n            <motion.div variants={itemVariants} className=\"lg:col-span-2\">\n              <div className=\"bg-card/50 border border-border/50 rounded-lg p-8 h-fit\">\n                <h3 className=\"text-xl font-semibold text-foreground mb-6\">\n                  Contact Information\n                </h3>\n\n                <div className=\"space-y-6\">\n                  {contactInfo.map((info, index) => {\n                    const IconComponent = info.icon;\n                    const content = (\n                      <div className=\"flex items-start gap-4 group\">\n                        <div className=\"w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors\">\n                          <IconComponent className=\"w-5 h-5 text-primary\" />\n                        </div>\n                        <div className=\"flex-1\">\n                          <p className=\"text-sm text-muted-foreground mb-1\">{info.label}</p>\n                          <p className=\"text-foreground group-hover:text-primary transition-colors\">\n                            {info.value}\n                          </p>\n                        </div>\n                      </div>\n                    );\n\n                    return info.href ? (\n                      <motion.a\n                        key={index}\n                        href={info.href}\n                        className=\"block hover:scale-105 transition-transform duration-200\"\n                        whileHover={{ x: 4 }}\n                      >\n                        {content}\n                      </motion.a>\n                    ) : (\n                      <motion.div key={index} className=\"block\">\n                        {content}\n                      </motion.div>\n                    );\n                  })}\n                </div>\n\n                {/* Social Links */}\n                <div className=\"mt-8 pt-6 border-t border-border/50\">\n                  <p className=\"text-sm text-muted-foreground mb-4\">Follow me on</p>\n                  <div className=\"flex gap-3\">\n                    {socialLinks.map((social, index) => {\n                      const IconComponent = social.icon;\n                      return (\n                        <motion.a\n                          key={index}\n                          href={social.href}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"w-10 h-10 bg-muted/50 rounded-lg flex items-center justify-center hover:bg-primary/20 transition-colors\"\n                          whileHover={{ scale: 1.1 }}\n                          whileTap={{ scale: 0.95 }}\n                          aria-label={social.label}\n                        >\n                          <IconComponent className=\"w-5 h-5 text-muted-foreground hover:text-primary transition-colors\" />\n                        </motion.a>\n                      );\n                    })}\n                  </div>\n                </div>\n\n                {/* Resume Button */}\n                <div className=\"mt-6\">\n                  <motion.button\n                    onClick={handleResumeClick}\n                    className=\"w-full bg-muted/10 border border-primary/20 text-primary rounded-lg font-medium px-6 py-3 transition-all duration-300 hover:shadow-lg hover:bg-primary hover:text-primary-foreground group\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <span className=\"flex items-center justify-center gap-2\">\n                      <FileText className=\"w-4 h-4\" />\n                      View Resume\n                    </span>\n                  </motion.button>\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Contact Form */}\n            <motion.div variants={itemVariants} className=\"lg:col-span-3\">\n              <div className=\"bg-card/50 border border-border/50 rounded-lg p-8\">\n                <h3 className=\"text-xl font-semibold text-foreground mb-6\">\n                  Send me a message\n                </h3>\n\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div className=\"grid sm:grid-cols-2 gap-6\">\n                    <div>\n                      <label htmlFor=\"name\" className=\"block text-sm font-medium text-foreground mb-2\">\n                        Name *\n                      </label>\n                      <input\n                        type=\"text\"\n                        id=\"name\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleInputChange}\n                        placeholder={FORM_CONFIG.placeholders.name}\n                        className={cn(\n                          'w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors',\n                          'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',\n                          errors.name ? 'border-red-500' : 'border-border'\n                        )}\n                      />\n                      {errors.name && (\n                        <p className=\"mt-1 text-sm text-red-500\">{errors.name}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label htmlFor=\"email\" className=\"block text-sm font-medium text-foreground mb-2\">\n                        Email *\n                      </label>\n                      <input\n                        type=\"email\"\n                        id=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        placeholder={FORM_CONFIG.placeholders.email}\n                        className={cn(\n                          'w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors',\n                          'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',\n                          errors.email ? 'border-red-500' : 'border-border'\n                        )}\n                      />\n                      {errors.email && (\n                        <p className=\"mt-1 text-sm text-red-500\">{errors.email}</p>\n                      )}\n                    </div>\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"subject\" className=\"block text-sm font-medium text-foreground mb-2\">\n                      Subject *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"subject\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleInputChange}\n                      placeholder={FORM_CONFIG.placeholders.subject}\n                      className={cn(\n                        'w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors',\n                        'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',\n                        errors.subject ? 'border-red-500' : 'border-border'\n                      )}\n                    />\n                    {errors.subject && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.subject}</p>\n                    )}\n                  </div>\n\n                  <div>\n                    <label htmlFor=\"message\" className=\"block text-sm font-medium text-foreground mb-2\">\n                      Message *\n                    </label>\n                    <textarea\n                      id=\"message\"\n                      name=\"message\"\n                      rows={6}\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      placeholder={FORM_CONFIG.placeholders.message}\n                      className={cn(\n                        'w-full px-4 py-3 rounded-lg border bg-background/50 transition-colors resize-none',\n                        'focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary',\n                        errors.message ? 'border-red-500' : 'border-border'\n                      )}\n                    />\n                    {errors.message && (\n                      <p className=\"mt-1 text-sm text-red-500\">{errors.message}</p>\n                    )}\n                  </div>\n\n                  <motion.button\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    className={cn(\n                      'w-full sm:w-auto px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium',\n                      'flex items-center justify-center gap-2 transition-all duration-300',\n                      'hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed',\n                      isSubmitting && 'animate-pulse'\n                    )}\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    {isSubmitting ? (\n                      <>\n                        <div className=\"w-4 h-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin\" />\n                        Sending...\n                      </>\n                    ) : (\n                      <>\n                        Send Message\n                        <Send className=\"w-4 h-4\" />\n                      </>\n                    )}\n                  </motion.button>\n\n                  {submitStatus === 'success' && (\n                    <motion.p\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className=\"text-green-600 text-sm\"\n                    >\n                      Thank you for your message! I'll get back to you soon.\n                    </motion.p>\n                  )}\n\n                  {submitStatus === 'error' && (\n                    <motion.p\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className=\"text-red-600 text-sm\"\n                    >\n                      Something went wrong. Please try again.\n                    </motion.p>\n                  )}\n                </form>\n              </div>\n            </motion.div>\n          </div>\n\n\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AA0BO,SAAS,eAAe,EAAE,SAAS,EAAuB;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAE/E,MAAM,oBAAoB;QACxB,8CAA8C;QAC9C,OAAO,IAAI,CAAC,6BAA6B;IAC3C;IAEA,MAAM,cAAc;QAClB;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,MAAM;QACR;QACA;YACE,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,eAAe;QACnB,MAAM,YAAwB,CAAC;QAE/B,kBAAkB;QAClB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,UAAU,IAAI,GAAG;QACnB,OAAO,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,uHAAA,CAAA,cAAW,CAAC,UAAU,CAAC,aAAa,EAAE;YACtE,UAAU,IAAI,GAAG,CAAC,sBAAsB,EAAE,uHAAA,CAAA,cAAW,CAAC,UAAU,CAAC,aAAa,CAAC,WAAW,CAAC;QAC7F;QAEA,mBAAmB;QACnB,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,uHAAA,CAAA,cAAW,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,GAAG;YAC7D,UAAU,KAAK,GAAG;QACpB;QAEA,qBAAqB;QACrB,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB;QAEA,qBAAqB;QACrB,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YAC5B,UAAU,OAAO,GAAG;QACtB,OAAO,IAAI,SAAS,OAAO,CAAC,MAAM,GAAG,uHAAA,CAAA,cAAW,CAAC,UAAU,CAAC,gBAAgB,EAAE;YAC5E,UAAU,OAAO,GAAG,CAAC,yBAAyB,EAAE,uHAAA,CAAA,cAAW,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,CAAC;QACtG;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAyB,EAAE;YACpC,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACnD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,gBAAgB;YAChB,YAAY;gBAAE,MAAM;gBAAI,OAAO;gBAAI,SAAS;gBAAI,SAAS;YAAG;QAC9D,EAAE,OAAO,OAAO;YACd,gBAAgB;QAClB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mEACA;;0BAIF,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;;sCAGzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;8CACZ,cAAA,8OAAC;wCAAK,WAAU;kDAAoG;;;;;;;;;;;8CAItH,8OAAC;oCAAE,WAAU;8CAAkE;;;;;;;;;;;;sCAMjF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;8CAC5C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAI3D,8OAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oDACtB,MAAM,gBAAgB,KAAK,IAAI;oDAC/B,MAAM,wBACJ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAc,WAAU;;;;;;;;;;;0EAE3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAsC,KAAK,KAAK;;;;;;kFAC7D,8OAAC;wEAAE,WAAU;kFACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;oDAMnB,OAAO,KAAK,IAAI,iBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDAEP,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,YAAY;4DAAE,GAAG;wDAAE;kEAElB;uDALI;;;;6EAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAAa,WAAU;kEAC/B;uDADc;;;;;gDAIrB;;;;;;0DAIF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAI,WAAU;kEACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;4DACxB,MAAM,gBAAgB,OAAO,IAAI;4DACjC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gEAEP,MAAM,OAAO,IAAI;gEACjB,QAAO;gEACP,KAAI;gEACJ,WAAU;gEACV,YAAY;oEAAE,OAAO;gEAAI;gEACzB,UAAU;oEAAE,OAAO;gEAAK;gEACxB,cAAY,OAAO,KAAK;0EAExB,cAAA,8OAAC;oEAAc,WAAU;;;;;;+DATpB;;;;;wDAYX;;;;;;;;;;;;0DAKJ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,SAAS;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;8DAExB,cAAA,8OAAC;wDAAK,WAAU;;0EACd,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;8CAC5C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6C;;;;;;0DAI3D,8OAAC;gDAAK,UAAU;gDAAc,WAAU;;kEACtC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAO,WAAU;kFAAiD;;;;;;kFAGjF,8OAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,IAAI;wEACpB,UAAU;wEACV,aAAa,uHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,IAAI;wEAC1C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yEACA,8EACA,OAAO,IAAI,GAAG,mBAAmB;;;;;;oEAGpC,OAAO,IAAI,kBACV,8OAAC;wEAAE,WAAU;kFAA6B,OAAO,IAAI;;;;;;;;;;;;0EAIzD,8OAAC;;kFACC,8OAAC;wEAAM,SAAQ;wEAAQ,WAAU;kFAAiD;;;;;;kFAGlF,8OAAC;wEACC,MAAK;wEACL,IAAG;wEACH,MAAK;wEACL,OAAO,SAAS,KAAK;wEACrB,UAAU;wEACV,aAAa,uHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,KAAK;wEAC3C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yEACA,8EACA,OAAO,KAAK,GAAG,mBAAmB;;;;;;oEAGrC,OAAO,KAAK,kBACX,8OAAC;wEAAE,WAAU;kFAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;kEAK5D,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAAiD;;;;;;0EAGpF,8OAAC;gEACC,MAAK;gEACL,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,OAAO;gEACvB,UAAU;gEACV,aAAa,uHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,OAAO;gEAC7C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yEACA,8EACA,OAAO,OAAO,GAAG,mBAAmB;;;;;;4DAGvC,OAAO,OAAO,kBACb,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,OAAO;;;;;;;;;;;;kEAI5D,8OAAC;;0EACC,8OAAC;gEAAM,SAAQ;gEAAU,WAAU;0EAAiD;;;;;;0EAGpF,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,MAAM;gEACN,OAAO,SAAS,OAAO;gEACvB,UAAU;gEACV,aAAa,uHAAA,CAAA,cAAW,CAAC,YAAY,CAAC,OAAO;gEAC7C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA,8EACA,OAAO,OAAO,GAAG,mBAAmB;;;;;;4DAGvC,OAAO,OAAO,kBACb,8OAAC;gEAAE,WAAU;0EAA6B,OAAO,OAAO;;;;;;;;;;;;kEAI5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,MAAK;wDACL,UAAU;wDACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,sEACA,mEACA,gBAAgB;wDAElB,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEAEvB,6BACC;;8EACE,8OAAC;oEAAI,WAAU;;;;;;gEAAwG;;yFAIzH;;gEAAE;8EAEA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;oDAKrB,iBAAiB,2BAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDACP,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,WAAU;kEACX;;;;;;oDAKF,iBAAiB,yBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDACP,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcrB", "debugId": null}}, {"offset": {"line": 4325, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { LandingScreen } from '@/components/sections/landing-screen';\nimport { HeroSection } from '@/components/sections/hero-section';\nimport { AboutSection } from '@/components/sections/about-section';\nimport { ProjectsSection } from '@/components/sections/projects-section';\nimport { ExperienceSection } from '@/components/sections/experience-section';\nimport { ContactSection } from '@/components/sections/contact-section';\nimport { useLanding } from '@/contexts/landing-context';\n\nexport default function Home() {\n  const { showLanding, handleLandingComplete } = useLanding();\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Landing Screen */}\n      {showLanding && <LandingScreen onComplete={handleLandingComplete} />}\n\n      {/* Main Content */}\n      <div className={showLanding ? 'opacity-0' : 'opacity-100 transition-opacity duration-1000'}>\n        {/* Hero Section */}\n        <HeroSection />\n\n        {/* About Section */}\n        <AboutSection />\n\n        {/* Projects Section */}\n        <ProjectsSection />\n\n        {/* Experience Section */}\n        <ExperienceSection />\n\n        {/* Contact Section */}\n        <ContactSection />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IAExD,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BAAe,8OAAC,mJAAA,CAAA,gBAAa;gBAAC,YAAY;;;;;;0BAG3C,8OAAC;gBAAI,WAAW,cAAc,cAAc;;kCAE1C,8OAAC,iJAAA,CAAA,cAAW;;;;;kCAGZ,8OAAC,kJAAA,CAAA,eAAY;;;;;kCAGb,8OAAC,qJAAA,CAAA,kBAAe;;;;;kCAGhB,8OAAC,uJAAA,CAAA,oBAAiB;;;;;kCAGlB,8OAAC,oJAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}]}