module.exports = {

"[next]/internal/font/google/inter_5802845b.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "inter_5802845b-module__9kuUBG__className",
  "variable": "inter_5802845b-module__9kuUBG__variable",
});
}}),
"[next]/internal/font/google/inter_5802845b.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_5802845b.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Inter', 'Inter Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/poppins_24a8cf86.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "poppins_24a8cf86-module__TFw21G__className",
  "variable": "poppins_24a8cf86-module__TFw21G__variable",
});
}}),
"[next]/internal/font/google/poppins_24a8cf86.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$poppins_24a8cf86$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/poppins_24a8cf86.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$poppins_24a8cf86$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Poppins', 'Poppins Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$poppins_24a8cf86$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$poppins_24a8cf86$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/src/components/layout/layout.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Layout": (()=>Layout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Layout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/layout.tsx <module evaluation>", "Layout");
}}),
"[project]/src/components/layout/layout.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Layout": (()=>Layout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const Layout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/layout.tsx", "Layout");
}}),
"[project]/src/components/layout/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$layout$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/layout/layout.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$layout$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/components/layout/layout.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$layout$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/lib/constants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ANIMATION_DURATIONS": (()=>ANIMATION_DURATIONS),
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "APP_CONFIG": (()=>APP_CONFIG),
    "BREAKPOINTS": (()=>BREAKPOINTS),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "FEATURES": (()=>FEATURES),
    "FILE_PATHS": (()=>FILE_PATHS),
    "FORM_CONFIG": (()=>FORM_CONFIG),
    "LANGUAGES": (()=>LANGUAGES),
    "MACOS_CONFIG": (()=>MACOS_CONFIG),
    "MACOS_DOCK_ITEMS": (()=>MACOS_DOCK_ITEMS),
    "NAV_ITEMS": (()=>NAV_ITEMS),
    "PARTICLE_CONFIG": (()=>PARTICLE_CONFIG),
    "PERFORMANCE_CONFIG": (()=>PERFORMANCE_CONFIG),
    "SCROLL_CONFIG": (()=>SCROLL_CONFIG),
    "SEO_CONFIG": (()=>SEO_CONFIG),
    "SOCIAL_LINKS": (()=>SOCIAL_LINKS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "THEME_CONFIG": (()=>THEME_CONFIG),
    "Z_INDEX": (()=>Z_INDEX)
});
const APP_CONFIG = {
    name: 'Portfolio',
    version: '2.0.0',
    environment: ("TURBOPACK compile-time value", "development"),
    baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
};
const NAV_ITEMS = [
    {
        id: 'home',
        label: 'Home',
        href: '#home'
    },
    {
        id: 'about',
        label: 'About',
        href: '#about'
    },
    {
        id: 'projects',
        label: 'Projects',
        href: '#projects'
    },
    {
        id: 'experience',
        label: 'Experience',
        href: '#experience'
    },
    {
        id: 'contact',
        label: 'Contact',
        href: '#contact'
    }
];
const LANGUAGES = [
    {
        code: 'en',
        name: 'English',
        greeting: 'Hello'
    },
    {
        code: 'es',
        name: 'Spanish',
        greeting: 'Hola'
    },
    {
        code: 'fr',
        name: 'French',
        greeting: 'Bonjour'
    },
    {
        code: 'de',
        name: 'German',
        greeting: 'Hallo'
    },
    {
        code: 'it',
        name: 'Italian',
        greeting: 'Ciao'
    },
    {
        code: 'pt',
        name: 'Portuguese',
        greeting: 'Olá'
    },
    {
        code: 'ru',
        name: 'Russian',
        greeting: 'Привет'
    },
    {
        code: 'ja',
        name: 'Japanese',
        greeting: 'こんにちは'
    },
    {
        code: 'ko',
        name: 'Korean',
        greeting: '안녕하세요'
    },
    {
        code: 'zh',
        name: 'Chinese',
        greeting: '你好'
    },
    {
        code: 'ar',
        name: 'Arabic',
        greeting: 'مرحبا'
    },
    {
        code: 'hi',
        name: 'Hindi',
        greeting: 'नमस्ते'
    }
];
const ANIMATION_DURATIONS = {
    fast: 150,
    normal: 250,
    slow: 400,
    languageSwitch: 250,
    pageTransition: 500,
    scrollIndicator: 1000,
    particleAnimation: 60000
};
const BREAKPOINTS = {
    mobile: 768,
    tablet: 1024,
    desktop: 1280,
    wide: 1536
};
const THEME_CONFIG = {
    defaultTheme: 'light',
    storageKey: 'portfolio-theme',
    transitionDuration: '0.3s'
};
const MACOS_CONFIG = {
    dock: {
        height: 80,
        iconSize: 48,
        hoverScale: 1.5,
        animationDuration: 200
    },
    window: {
        minWidth: 400,
        minHeight: 300,
        defaultWidth: 800,
        defaultHeight: 600,
        headerHeight: 30,
        borderRadius: 8
    },
    desktop: {
        gridSize: 80,
        iconSize: 64,
        folderIconSize: 72
    }
};
const MACOS_DOCK_ITEMS = [
    {
        id: 'calculator',
        name: 'Calculator',
        icon: '/icons/calculator.png',
        tooltip: 'Calculator',
        action: 'calculator'
    },
    {
        id: 'github',
        name: 'GitHub',
        icon: '/icons/github.png',
        tooltip: 'GitHub Profile',
        action: 'github'
    },
    {
        id: 'linkedin',
        name: 'LinkedIn',
        icon: '/icons/linkedin.png',
        tooltip: 'LinkedIn Profile',
        action: 'linkedin'
    }
];
const PARTICLE_CONFIG = {
    count: 50,
    maxSize: 3,
    minSize: 1,
    speed: 0.5,
    opacity: {
        min: 0.1,
        max: 0.8
    },
    colors: [
        '#ffffff',
        '#f0f0f0',
        '#e0e0e0'
    ]
};
const FORM_CONFIG = {
    validation: {
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        phone: /^[\+]?[1-9][\d]{0,15}$/,
        minNameLength: 2,
        maxNameLength: 50,
        minMessageLength: 10,
        maxMessageLength: 1000
    },
    placeholders: {
        name: 'Your Name',
        email: '<EMAIL>',
        subject: 'Subject',
        message: 'Your message...'
    }
};
const SEO_CONFIG = {
    title: 'Portfolio - Full Stack Developer',
    description: 'Professional portfolio showcasing full-stack development projects, skills, and experience.',
    keywords: [
        'full-stack developer',
        'web development',
        'react',
        'next.js',
        'typescript',
        'portfolio',
        'software engineer'
    ],
    author: 'Your Name',
    siteUrl: APP_CONFIG.baseUrl,
    image: '/assets/images/og-image.jpg',
    twitterHandle: '@yourhandle'
};
const SOCIAL_LINKS = [
    {
        platform: 'GitHub',
        url: 'https://github.com/yourusername',
        icon: 'Github',
        label: 'GitHub Profile'
    },
    {
        platform: 'LinkedIn',
        url: 'https://linkedin.com/in/yourusername',
        icon: 'Linkedin',
        label: 'LinkedIn Profile'
    },
    {
        platform: 'Twitter',
        url: 'https://twitter.com/yourusername',
        icon: 'Twitter',
        label: 'Twitter Profile'
    },
    {
        platform: 'Email',
        url: 'mailto:<EMAIL>',
        icon: 'Mail',
        label: 'Send Email'
    }
];
const SCROLL_CONFIG = {
    smoothScrollDuration: 800,
    scrollOffset: 80,
    scrollThreshold: 100,
    indicatorFadeDistance: 200
};
const PERFORMANCE_CONFIG = {
    imageOptimization: {
        quality: 85,
        formats: [
            'webp',
            'jpg'
        ],
        sizes: [
            640,
            768,
            1024,
            1280,
            1536
        ]
    },
    lazyLoading: {
        rootMargin: '50px',
        threshold: 0.1
    },
    debounceDelay: 300,
    throttleDelay: 100
};
const ERROR_MESSAGES = {
    generic: 'Something went wrong. Please try again.',
    network: 'Network error. Please check your connection.',
    validation: {
        required: 'This field is required.',
        email: 'Please enter a valid email address.',
        minLength: (min)=>`Must be at least ${min} characters long.`,
        maxLength: (max)=>`Must be no more than ${max} characters long.`
    },
    form: {
        submitError: 'Failed to send message. Please try again.',
        submitSuccess: 'Message sent successfully!'
    }
};
const SUCCESS_MESSAGES = {
    form: {
        submitted: 'Thank you for your message! I\'ll get back to you soon.'
    },
    theme: {
        switched: 'Theme updated successfully.'
    }
};
const FILE_PATHS = {
    resume: '/assets/resume/resume.pdf',
    images: {
        profile: '/assets/images/profile.jpg',
        profileHover: '/assets/images/profile-hover.jpg',
        ogImage: '/assets/images/og-image.jpg',
        favicon: '/favicon.ico'
    },
    icons: {
        logo: '/icons/logo.svg',
        logoLight: '/icons/logo-light.svg',
        logoDark: '/icons/logo-dark.svg'
    }
};
const API_ENDPOINTS = {
    contact: '/api/contact',
    analytics: '/api/analytics'
};
const FEATURES = {
    analytics: ("TURBOPACK compile-time value", "development") === 'production',
    errorReporting: ("TURBOPACK compile-time value", "development") === 'production',
    performanceMonitoring: ("TURBOPACK compile-time value", "development") === 'production',
    darkMode: true,
    animations: true,
    particleSystem: true,
    macosInterface: true
};
const Z_INDEX = {
    base: 0,
    dropdown: 10,
    sticky: 20,
    navbar: 30,
    modal: 40,
    tooltip: 50,
    notification: 60,
    overlay: 70
};
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_5802845b.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$poppins_24a8cf86$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/poppins_24a8cf86.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/layout.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
const metadata = {
    title: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].title,
    description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].description,
    keywords: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].keywords,
    authors: [
        {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].author
        }
    ],
    creator: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].author,
    openGraph: {
        type: "website",
        locale: "en_US",
        url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].siteUrl,
        title: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].title,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].description,
        siteName: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].title,
        images: [
            {
                url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].image,
                width: 1200,
                height: 630,
                alt: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].title
            }
        ]
    },
    twitter: {
        card: "summary_large_image",
        title: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].title,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].description,
        images: [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].image
        ],
        creator: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEO_CONFIG"].twitterHandle
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            "max-video-preview": -1,
            "max-image-preview": "large",
            "max-snippet": -1
        }
    },
    verification: {
        google: "your-google-verification-code"
    }
};
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        suppressHydrationWarning: true,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
            className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_5802845b$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$poppins_24a8cf86$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} font-inter antialiased`,
            style: {
                fontFamily: 'var(--font-inter), var(--font-poppins), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif'
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Layout"], {
                children: children
            }, void 0, false, {
                fileName: "[project]/src/app/layout.tsx",
                lineNumber: 78,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/layout.tsx",
            lineNumber: 72,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e57320fa._.js.map