{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/sections/landing-screen.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { LANGUAGES, ANIMATION_DURATIONS } from '@/lib/constants';\n\ninterface LandingScreenProps {\n  onComplete?: () => void;\n}\n\nexport function LandingScreen({ onComplete }: LandingScreenProps) {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isVisible, setIsVisible] = useState(true);\n  const [showParticles, setShowParticles] = useState(false);\n\n  // Multi-language animation effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % LANGUAGES.length);\n    }, ANIMATION_DURATIONS.languageSwitch);\n\n    // Start exit animation after showing all languages\n    const exitTimer = setTimeout(() => {\n      setShowParticles(true);\n      \n      // Start exit animation\n      setTimeout(() => {\n        setIsVisible(false);\n        \n        // Call onComplete callback after animation\n        setTimeout(() => {\n          onComplete?.();\n        }, 800);\n      }, 300);\n    }, 1500);\n\n    return () => {\n      clearInterval(interval);\n      clearTimeout(exitTimer);\n    };\n  }, [onComplete]);\n\n  // Background panning animation variants\n  const backgroundVariants = {\n    animate: {\n      x: ['-25%', '-75%'],\n      y: ['-25%', '-75%'],\n      transition: {\n        duration: 5,\n        repeat: Infinity,\n        ease: 'linear',\n      },\n    },\n  };\n\n  // Landing screen exit animation\n  const landingVariants = {\n    visible: {\n      y: 0,\n      rotateX: 0,\n      opacity: 1,\n      transition: {\n        duration: 0.5,\n        ease: 'easeOut',\n      },\n    },\n    hidden: {\n      y: '-100%',\n      rotateX: 10,\n      opacity: 0,\n      transition: {\n        duration: 1.2,\n        ease: [0.6, 0.1, 0.3, 1],\n      },\n    },\n  };\n\n  // Message animation variants\n  const messageVariants = {\n    hidden: {\n      opacity: 0,\n      scale: 1,\n      transition: {\n        duration: 0.3,\n        ease: 'easeInOut',\n      },\n    },\n    visible: {\n      opacity: 1,\n      scale: 1.1,\n      transition: {\n        duration: 0.3,\n        ease: 'easeInOut',\n      },\n    },\n  };\n\n  // Particle animation variants\n  const particleVariants = {\n    initial: {\n      scale: 0,\n      opacity: 0,\n    },\n    animate: {\n      scale: 1,\n      opacity: 0.7,\n      x: Math.random() * 200 - 100,\n      y: Math.random() * 200 - 100,\n      transition: {\n        duration: 1.5,\n        ease: 'easeOut',\n      },\n    },\n    exit: {\n      scale: 0,\n      opacity: 0,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed inset-0 z-[2000] flex items-center justify-center overflow-hidden\"\n          style={{\n            background: 'linear-gradient(135deg, #000, #222)',\n            perspective: '1000px',\n          }}\n          variants={landingVariants}\n          initial=\"visible\"\n          animate=\"visible\"\n          exit=\"hidden\"\n        >\n          {/* Background Effect */}\n          <motion.div\n            className=\"absolute w-[200%] h-[200%] opacity-20\"\n            style={{\n              background: 'radial-gradient(circle, rgba(255,255,255,0.08) 0%, rgba(0,0,0,0) 70%)',\n            }}\n            variants={backgroundVariants}\n            animate=\"animate\"\n          />\n\n          {/* Message Container */}\n          <div className=\"relative h-[120px] w-full text-center\">\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={currentIndex}\n                className=\"absolute inset-0 flex items-center justify-center\"\n                variants={messageVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                exit=\"hidden\"\n              >\n                <h1 \n                  className=\"text-5xl md:text-6xl font-semibold text-white/95\"\n                  style={{\n                    textShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',\n                  }}\n                >\n                  {LANGUAGES[currentIndex].greeting}\n                </h1>\n              </motion.div>\n            </AnimatePresence>\n          </div>\n\n          {/* Particles Effect */}\n          <AnimatePresence>\n            {showParticles && (\n              <>\n                {Array.from({ length: 20 }).map((_, i) => (\n                  <motion.div\n                    key={i}\n                    className=\"absolute w-1.5 h-1.5 bg-white/70 rounded-full pointer-events-none\"\n                    style={{\n                      left: `${50 + (Math.random() - 0.5) * 20}%`,\n                      top: `${50 + (Math.random() - 0.5) * 20}%`,\n                    }}\n                    variants={particleVariants}\n                    initial=\"initial\"\n                    animate=\"animate\"\n                    exit=\"exit\"\n                  />\n                ))}\n              </>\n            )}\n          </AnimatePresence>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAUO,SAAS,cAAc,EAAE,UAAU,EAAsB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,uHAAA,CAAA,YAAS,CAAC,MAAM;QACzD,GAAG,uHAAA,CAAA,sBAAmB,CAAC,cAAc;QAErC,mDAAmD;QACnD,MAAM,YAAY,WAAW;YAC3B,iBAAiB;YAEjB,uBAAuB;YACvB,WAAW;gBACT,aAAa;gBAEb,2CAA2C;gBAC3C,WAAW;oBACT;gBACF,GAAG;YACL,GAAG;QACL,GAAG;QAEH,OAAO;YACL,cAAc;YACd,aAAa;QACf;IACF,GAAG;QAAC;KAAW;IAEf,wCAAwC;IACxC,MAAM,qBAAqB;QACzB,SAAS;YACP,GAAG;gBAAC;gBAAQ;aAAO;YACnB,GAAG;gBAAC;gBAAQ;aAAO;YACnB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,SAAS;YACP,GAAG;YACH,SAAS;YACT,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,QAAQ;YACN,GAAG;YACH,SAAS;YACT,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAK;oBAAK;oBAAK;iBAAE;YAC1B;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,SAAS;YACP,SAAS;YACT,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM,mBAAmB;QACvB,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,SAAS;YACP,OAAO;YACP,SAAS;YACT,GAAG,KAAK,MAAM,KAAK,MAAM;YACzB,GAAG,KAAK,MAAM,KAAK,MAAM;YACzB,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,MAAM;YACJ,OAAO;YACP,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,OAAO;gBACL,YAAY;gBACZ,aAAa;YACf;YACA,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,MAAK;;8BAGL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBACL,YAAY;oBACd;oBACA,UAAU;oBACV,SAAQ;;;;;;8BAIV,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;sCAEL,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY;gCACd;0CAEC,uHAAA,CAAA,YAAS,CAAC,aAAa,CAAC,QAAQ;;;;;;2BAb9B;;;;;;;;;;;;;;;8BAoBX,8OAAC,yLAAA,CAAA,kBAAe;8BACb,+BACC;kCACG,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;oCAC3C,KAAK,GAAG,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;gCAC5C;gCACA,UAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,MAAK;+BATA;;;;;;;;;;;;;;;;;;;;;;AAmBzB", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/interactive/scroll-indicator.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ChevronDown } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { SCROLL_CONFIG } from '@/lib/constants';\n\ninterface ScrollIndicatorProps {\n  className?: string;\n}\n\nexport function ScrollIndicator({ className }: ScrollIndicatorProps) {\n  const [isVisible, setIsVisible] = useState(true);\n  const [isOnHomePage, setIsOnHomePage] = useState(true);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrollY = window.scrollY;\n      const homeSection = document.getElementById('home');\n      \n      if (homeSection) {\n        const homeSectionHeight = homeSection.offsetHeight;\n        const isInHomeSection = scrollY < homeSectionHeight - 100;\n        \n        setIsOnHomePage(isInHomeSection);\n        setIsVisible(isInHomeSection && scrollY < SCROLL_CONFIG.indicatorFadeDistance);\n      }\n    };\n\n    // Initial check\n    handleScroll();\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleClick = () => {\n    const aboutSection = document.getElementById('about');\n    if (aboutSection) {\n      aboutSection.scrollIntoView({ \n        behavior: 'smooth',\n        block: 'start'\n      });\n    }\n  };\n\n  const containerVariants = {\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5,\n        ease: 'easeOut',\n      },\n    },\n    hidden: {\n      opacity: 0,\n      y: 20,\n      transition: {\n        duration: 0.3,\n        ease: 'easeIn',\n      },\n    },\n  };\n\n  const arrowVariants = {\n    animate: {\n      y: [0, 8, 0],\n      transition: {\n        duration: 1.5,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      },\n    },\n  };\n\n  const textVariants = {\n    animate: {\n      opacity: [0.7, 1, 0.7],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: 'easeInOut',\n      },\n    },\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && isOnHomePage && (\n        <motion.div\n          className={cn(\n            'absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center cursor-pointer z-10',\n            className\n          )}\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          exit=\"hidden\"\n          onClick={handleClick}\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          {/* Scroll Text */}\n          <motion.span\n            className=\"text-sm text-muted-foreground mb-2 font-medium\"\n            variants={textVariants}\n            animate=\"animate\"\n          >\n            Scroll Down\n          </motion.span>\n\n          {/* Arrow Container */}\n          <div className=\"relative\">\n            {/* First Arrow */}\n            <motion.div\n              variants={arrowVariants}\n              animate=\"animate\"\n            >\n              <ChevronDown className=\"w-6 h-6 text-muted-foreground\" />\n            </motion.div>\n\n            {/* Second Arrow (offset) */}\n            <motion.div\n              className=\"absolute top-0 left-0\"\n              variants={arrowVariants}\n              animate=\"animate\"\n              style={{ marginTop: '-8px' }}\n              transition={{ delay: 0.2 }}\n            >\n              <ChevronDown className=\"w-6 h-6 text-muted-foreground opacity-60\" />\n            </motion.div>\n          </div>\n\n          {/* Glow effect on hover */}\n          <motion.div\n            className=\"absolute inset-0 rounded-full bg-primary/20 blur-xl opacity-0\"\n            whileHover={{ opacity: 1 }}\n            transition={{ duration: 0.3 }}\n          />\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAYO,SAAS,gBAAgB,EAAE,SAAS,EAAwB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,UAAU,OAAO,OAAO;YAC9B,MAAM,cAAc,SAAS,cAAc,CAAC;YAE5C,IAAI,aAAa;gBACf,MAAM,oBAAoB,YAAY,YAAY;gBAClD,MAAM,kBAAkB,UAAU,oBAAoB;gBAEtD,gBAAgB;gBAChB,aAAa,mBAAmB,UAAU,uHAAA,CAAA,gBAAa,CAAC,qBAAqB;YAC/E;QACF;QAEA,gBAAgB;QAChB;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,eAAe,SAAS,cAAc,CAAC;QAC7C,IAAI,cAAc;YAChB,aAAa,cAAc,CAAC;gBAC1B,UAAU;gBACV,OAAO;YACT;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;QACA,QAAQ;YACN,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,SAAS;YACP,GAAG;gBAAC;gBAAG;gBAAG;aAAE;YACZ,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YACP,SAAS;gBAAC;gBAAK;gBAAG;aAAI;YACtB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,aAAa,8BACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wGACA;YAEF,UAAU;YACV,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,SAAS;YACT,YAAY;gBAAE,OAAO;YAAI;YACzB,UAAU;gBAAE,OAAO;YAAK;;8BAGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,WAAU;oBACV,UAAU;oBACV,SAAQ;8BACT;;;;;;8BAKD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;sCAER,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAIzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;4BACV,SAAQ;4BACR,OAAO;gCAAE,WAAW;4BAAO;4BAC3B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAK3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,YAAY;wBAAE,SAAS;oBAAE;oBACzB,YAAY;wBAAE,UAAU;oBAAI;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/common/social-icons.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Github, Linkedin, Twitter, Mail } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { SOCIAL_LINKS } from '@/lib/constants';\n\ninterface SocialIconsProps {\n  className?: string;\n}\n\nexport function SocialIcons({ className }: SocialIconsProps) {\n  const iconMap = {\n    Github,\n    Linkedin,\n    Twitter,\n    Mail,\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5,\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      className={cn('flex items-center gap-4', className)}\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {SOCIAL_LINKS.map((link, index) => {\n        const IconComponent = iconMap[link.icon as keyof typeof iconMap];\n        \n        return (\n          <motion.a\n            key={link.platform}\n            href={link.url}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"group relative p-3 rounded-full bg-muted/50 hover:bg-muted transition-all duration-300\"\n            variants={itemVariants}\n            whileHover={{ \n              scale: 1.1,\n              rotate: 5,\n            }}\n            whileTap={{ scale: 0.95 }}\n            aria-label={link.label}\n          >\n            {/* Background glow effect */}\n            <motion.div\n              className=\"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              initial={false}\n              animate={{\n                scale: [1, 1.2, 1],\n              }}\n              transition={{\n                duration: 2,\n                repeat: Infinity,\n                ease: 'easeInOut',\n              }}\n            />\n            \n            {/* Icon */}\n            <IconComponent className=\"w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors duration-300 relative z-10\" />\n            \n            {/* Tooltip */}\n            <motion.div\n              className=\"absolute -top-12 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap\"\n              initial={{ y: 10, opacity: 0 }}\n              whileHover={{ y: 0, opacity: 1 }}\n            >\n              {link.platform}\n              <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-popover\" />\n            </motion.div>\n          </motion.a>\n        );\n      })}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;AAYO,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,UAAU;QACd,QAAA,sMAAA,CAAA,SAAM;QACN,UAAA,0MAAA,CAAA,WAAQ;QACR,SAAA,wMAAA,CAAA,UAAO;QACP,MAAA,kMAAA,CAAA,OAAI;IACN;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACzC,UAAU;QACV,SAAQ;QACR,SAAQ;kBAEP,uHAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,MAAM;YACvB,MAAM,gBAAgB,OAAO,CAAC,KAAK,IAAI,CAAyB;YAEhE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gBAEP,MAAM,KAAK,GAAG;gBACd,QAAO;gBACP,KAAI;gBACJ,WAAU;gBACV,UAAU;gBACV,YAAY;oBACV,OAAO;oBACP,QAAQ;gBACV;gBACA,UAAU;oBAAE,OAAO;gBAAK;gBACxB,cAAY,KAAK,KAAK;;kCAGtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;wBACT,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,8OAAC;wBAAc,WAAU;;;;;;kCAGzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,YAAY;4BAAE,GAAG;4BAAG,SAAS;wBAAE;;4BAE9B,KAAK,QAAQ;0CACd,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;eArCZ,KAAK,QAAQ;;;;;QAyCxB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/sections/hero-section.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { <PERSON>R<PERSON>, Code, Github, Linkedin, Twitter } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { ScrollIndicator } from '@/components/interactive/scroll-indicator';\nimport { SocialIcons } from '@/components/common/social-icons';\n\ninterface HeroSectionProps {\n  className?: string;\n}\n\nexport function HeroSection({ className }: HeroSectionProps) {\n  const handleGetInTouch = () => {\n    const contactSection = document.getElementById('contact');\n    if (contactSection) {\n      contactSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  const handleViewProjects = () => {\n    const projectsSection = document.getElementById('projects');\n    if (projectsSection) {\n      projectsSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section \n      id=\"home\" \n      className={cn(\n        'min-h-screen flex items-center justify-center relative overflow-hidden',\n        className\n      )}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\">\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center\">\n          {/* Text Container */}\n          <motion.div\n            className=\"text-container space-y-8 lg:pr-8\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            {/* Online Status */}\n            <motion.div\n              className=\"inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm font-medium\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 0.4 }}\n            >\n              <motion.span\n                className=\"w-2 h-2 bg-green-500 rounded-full\"\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [1, 0.7, 1],\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: 'easeInOut',\n                }}\n              />\n              Available for work\n            </motion.div>\n\n            {/* Main Heading */}\n            <motion.h1\n              className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n            >\n              Hi, I'm Narendra{' '}\n              <motion.span\n                className=\"inline-block\"\n                animate={{\n                  rotate: [0, 14, -8, 14, -4, 10, 0],\n                }}\n                transition={{\n                  duration: 2.5,\n                  repeat: Infinity,\n                  repeatDelay: 3,\n                  ease: 'easeInOut',\n                }}\n              >\n                👋\n              </motion.span>\n            </motion.h1>\n\n            {/* Bio Container */}\n            <motion.div\n              className=\"bio-container space-y-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.8 }}\n            >\n              <h2 className=\"text-xl sm:text-2xl font-semibold text-foreground\">\n                No-Code Developer | AI Prompt Engineer | Mobile App Creator\n              </h2>\n              <p className=\"text-lg text-muted-foreground leading-relaxed max-w-2xl\">\n                Recent Computer Science and Engineering graduate specializing in AI-assisted development and no-code solutions. \n                I leverage modern AI tools to build efficient SaaS applications, mobile apps, and automation workflows. \n                With expertise in cloud backends and API integration, I create cost-effective digital solutions with minimal traditional coding.\n              </p>\n            </motion.div>\n\n            {/* Button Container */}\n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.0 }}\n            >\n              <motion.button\n                onClick={handleGetInTouch}\n                className=\"group relative px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span className=\"relative z-10 flex items-center gap-2\">\n                  Get in Touch\n                  <ArrowRight className=\"w-4 h-4 transition-transform group-hover:translate-x-1\" />\n                </span>\n                <motion.div\n                  className=\"absolute inset-0 bg-gradient-to-r from-primary to-primary/80\"\n                  initial={{ x: '-100%' }}\n                  whileHover={{ x: 0 }}\n                  transition={{ duration: 0.3 }}\n                />\n              </motion.button>\n\n              <motion.button\n                onClick={handleViewProjects}\n                className=\"group relative px-8 py-3 border border-border text-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-primary/50\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <span className=\"relative z-10 flex items-center gap-2\">\n                  View Projects\n                  <Code className=\"w-4 h-4 transition-transform group-hover:rotate-12\" />\n                </span>\n                <motion.div\n                  className=\"absolute inset-0 bg-muted/50\"\n                  initial={{ x: '-100%' }}\n                  whileHover={{ x: 0 }}\n                  transition={{ duration: 0.3 }}\n                />\n              </motion.button>\n            </motion.div>\n\n            {/* Social Icons */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.2 }}\n            >\n              <SocialIcons />\n            </motion.div>\n          </motion.div>\n\n          {/* Image Container */}\n          <motion.div\n            className=\"image-container flex justify-center lg:justify-end\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1, delay: 0.4 }}\n          >\n            <div className=\"relative w-80 h-80 lg:w-96 lg:h-96\">\n              <motion.div\n                className=\"morph-container relative w-full h-full rounded-3xl overflow-hidden shadow-2xl\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                {/* Background Image */}\n                <motion.div\n                  className=\"absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500\"\n                  initial={{ opacity: 1 }}\n                  whileHover={{ opacity: 0.8 }}\n                  transition={{ duration: 0.3 }}\n                />\n                \n                {/* Placeholder for actual images */}\n                <motion.div\n                  className=\"absolute inset-0 flex items-center justify-center text-white text-lg font-medium\"\n                  initial={{ opacity: 0.8 }}\n                  whileHover={{ opacity: 1 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  Profile Image\n                </motion.div>\n\n                {/* Hover overlay */}\n                <motion.div\n                  className=\"absolute inset-0 bg-black/20\"\n                  initial={{ opacity: 0 }}\n                  whileHover={{ opacity: 1 }}\n                  transition={{ duration: 0.3 }}\n                />\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <ScrollIndicator />\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;AAaO,SAAS,YAAY,EAAE,SAAS,EAAoB;IACzD,MAAM,mBAAmB;QACvB,MAAM,iBAAiB,SAAS,cAAc,CAAC;QAC/C,IAAI,gBAAgB;YAClB,eAAe,cAAc,CAAC;gBAAE,UAAU;YAAS;QACrD;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,kBAAkB,SAAS,cAAc,CAAC;QAChD,IAAI,iBAAiB;YACnB,gBAAgB,cAAc,CAAC;gBAAE,UAAU;YAAS;QACtD;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAGxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAU;4CACV,SAAS;gDACP,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;gDAClB,SAAS;oDAAC;oDAAG;oDAAK;iDAAE;4CACtB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;;;;;;wCACA;;;;;;;8CAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;wCACzC;wCACkB;sDACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAU;4CACV,SAAS;gDACP,QAAQ;oDAAC;oDAAG;oDAAI,CAAC;oDAAG;oDAAI,CAAC;oDAAG;oDAAI;iDAAE;4CACpC;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,aAAa;gDACb,MAAM;4CACR;sDACD;;;;;;;;;;;;8CAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,8OAAC;4CAAE,WAAU;sDAA0D;;;;;;;;;;;;8CAQzE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,8OAAC;oDAAK,WAAU;;wDAAwC;sEAEtD,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;8DAExB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,GAAG;oDAAQ;oDACtB,YAAY;wDAAE,GAAG;oDAAE;oDACnB,YAAY;wDAAE,UAAU;oDAAI;;;;;;;;;;;;sDAIhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,8OAAC;oDAAK,WAAU;;wDAAwC;sEAEtD,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;8DAElB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,GAAG;oDAAQ;oDACtB,YAAY;wDAAE,GAAG;oDAAE;oDACnB,YAAY;wDAAE,UAAU;oDAAI;;;;;;;;;;;;;;;;;;8CAMlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC,+IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sCAKhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;sCAEtC,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;;sDAG5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,YAAY;gDAAE,SAAS;4CAAI;4CAC3B,YAAY;gDAAE,UAAU;4CAAI;;;;;;sDAI9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;4CAAI;4CACxB,YAAY;gDAAE,SAAS;4CAAE;4CACzB,YAAY;gDAAE,UAAU;4CAAI;sDAC7B;;;;;;sDAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;4CAAE;4CACtB,YAAY;gDAAE,SAAS;4CAAE;4CACzB,YAAY;gDAAE,UAAU;4CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,8OAAC,wJAAA,CAAA,kBAAe;;;;;;;;;;;AAGtB", "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/components/sections/about-section.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { GraduationCap, Calendar, Award } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface AboutSectionProps {\n  className?: string;\n}\n\nexport function AboutSection({ className }: AboutSectionProps) {\n  const educationData = [\n    {\n      year: 'Expected 2026',\n      degree: 'Bachelor of Engineering in Computer Science and Engineering',\n      institution: 'NBKRIST College Autonomous',\n      grade: 'CGPA: 8.2 (Current)',\n    },\n    {\n      year: 'Graduated 2022',\n      degree: 'Intermediate',\n      institution: 'Narayana Junior College, State Board',\n      grade: 'CGPA: 5.55',\n    },\n    {\n      year: 'Graduated 2020',\n      degree: 'SSC',\n      institution: 'Narayana EM High School, State Board',\n      grade: 'CGPA: 9.88',\n    },\n  ];\n\n  const skillCategories = [\n    {\n      title: 'No-Code/Low-Code',\n      skills: ['SaaS development using AI-assisted tools & platforms'],\n    },\n    {\n      title: 'Cloud & Backend',\n      skills: [\n        'Supabase & Firebase – auth, DB, storage',\n        'API Integration & key management',\n        'Cost-optimized usage of 3rd-party services',\n      ],\n    },\n    {\n      title: 'Mobile Development',\n      skills: [\n        'Android & iOS dev via AI tools',\n        'Android Studio',\n        'Xcode',\n      ],\n    },\n    {\n      title: 'AI & Automation',\n      skills: [\n        'AI Prompt Engineering with low-iteration design',\n        'Workflow automation using n8n',\n        'Telegram bots for info delivery & engagement',\n      ],\n    },\n    {\n      title: 'Web Development',\n      skills: [\n        'HTML',\n        'CSS',\n        'JavaScript',\n        'Basic front-end tasks',\n      ],\n    },\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n      },\n    },\n  };\n\n  return (\n    <section \n      id=\"about\" \n      className={cn(\n        'min-h-screen py-20 bg-muted/20 relative overflow-hidden',\n        className\n      )}\n    >\n      {/* Background decoration */}\n      <div className=\"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-primary/5 to-transparent\" />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n        >\n          {/* Section Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl font-bold text-foreground mb-4\">\n              About Me\n            </h2>\n            <div className=\"w-20 h-1 bg-primary mx-auto rounded-full\" />\n          </motion.div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16\">\n            {/* About Text */}\n            <motion.div variants={itemVariants} className=\"space-y-8\">\n              <div className=\"prose prose-lg dark:prose-invert max-w-none\">\n                <p className=\"text-lg text-muted-foreground leading-relaxed\">\n                  Recent Computer Science and Engineering graduate specializing in no-code/low-code development and AI-assisted tools. \n                  I focus on creating efficient digital solutions with minimal traditional coding, leveraging AI tools, cloud backends, \n                  and automation workflows. I'm seeking opportunities where I can apply my skills in AI prompt engineering, SaaS development, \n                  and mobile app creation to deliver cost-effective and innovative solutions.\n                </p>\n              </div>\n\n              {/* Education Section */}\n              <div className=\"education-container\">\n                <motion.h3 \n                  className=\"text-2xl font-semibold text-foreground mb-6 flex items-center gap-2\"\n                  variants={itemVariants}\n                >\n                  <GraduationCap className=\"w-6 h-6 text-primary\" />\n                  Education\n                </motion.h3>\n                \n                <div className=\"space-y-6\">\n                  {educationData.map((edu, index) => (\n                    <motion.div\n                      key={index}\n                      className=\"education-item flex gap-4 p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors\"\n                      variants={itemVariants}\n                      whileHover={{ scale: 1.02 }}\n                      transition={{ duration: 0.2 }}\n                    >\n                      <div className=\"education-year flex-shrink-0\">\n                        <div className=\"flex items-center gap-2 text-sm font-medium text-primary\">\n                          <Calendar className=\"w-4 h-4\" />\n                          {edu.year}\n                        </div>\n                      </div>\n                      <div className=\"education-details flex-1\">\n                        <h4 className=\"font-semibold text-foreground mb-1\">{edu.degree}</h4>\n                        <p className=\"text-muted-foreground mb-1\">{edu.institution}</p>\n                        <p className=\"text-sm text-muted-foreground\">{edu.grade}</p>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n\n            {/* Skills Section */}\n            <motion.div variants={itemVariants} className=\"skills-container\">\n              <motion.h3 \n                className=\"text-2xl font-semibold text-foreground mb-6 flex items-center gap-2\"\n                variants={itemVariants}\n              >\n                <Award className=\"w-6 h-6 text-primary\" />\n                Skills\n              </motion.h3>\n              \n              <div className=\"skills-grid space-y-6\">\n                {skillCategories.map((category, index) => (\n                  <motion.div\n                    key={index}\n                    className=\"skill-category p-4 rounded-lg bg-background/50 border border-border/50 hover:border-primary/30 transition-colors\"\n                    variants={itemVariants}\n                    whileHover={{ scale: 1.02 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    <h4 className=\"font-semibold text-foreground mb-3\">{category.title}</h4>\n                    <ul className=\"skills-list space-y-2\">\n                      {category.skills.map((skill, skillIndex) => (\n                        <li \n                          key={skillIndex}\n                          className=\"text-muted-foreground flex items-start gap-2\"\n                        >\n                          <span className=\"w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0\" />\n                          {skill}\n                        </li>\n                      ))}\n                    </ul>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AALA;;;;;AAWO,SAAS,aAAa,EAAE,SAAS,EAAqB;IAC3D,MAAM,gBAAgB;QACpB;YACE,MAAM;YACN,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,QAAQ;YACR,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,QAAQ;YACR,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,QAAQ;gBAAC;aAAuD;QAClE;QACA;YACE,OAAO;YACP,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,QAAQ;gBACN;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QACC,IAAG;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;;0BAIF,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAS;;sCAGzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;sDAS/D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oDACR,WAAU;oDACV,UAAU;;sEAEV,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAyB;;;;;;;8DAIpD,8OAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,KAAK,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,WAAU;4DACV,UAAU;4DACV,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,YAAY;gEAAE,UAAU;4DAAI;;8EAE5B,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EACnB,IAAI,IAAI;;;;;;;;;;;;8EAGb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAsC,IAAI,MAAM;;;;;;sFAC9D,8OAAC;4EAAE,WAAU;sFAA8B,IAAI,WAAW;;;;;;sFAC1D,8OAAC;4EAAE,WAAU;sFAAiC,IAAI,KAAK;;;;;;;;;;;;;2DAfpD;;;;;;;;;;;;;;;;;;;;;;8CAwBf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,UAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAI5C,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,UAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,YAAY;wDAAE,UAAU;oDAAI;;sEAE5B,8OAAC;4DAAG,WAAU;sEAAsC,SAAS,KAAK;;;;;;sEAClE,8OAAC;4DAAG,WAAU;sEACX,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BAC3B,8OAAC;oEAEC,WAAU;;sFAEV,8OAAC;4EAAK,WAAU;;;;;;wEACf;;mEAJI;;;;;;;;;;;mDAVN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BzB", "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/development/portfilio/portfilio/portfolio-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { LandingScreen } from '@/components/sections/landing-screen';\nimport { HeroSection } from '@/components/sections/hero-section';\nimport { AboutSection } from '@/components/sections/about-section';\n\nexport default function Home() {\n  const [showLanding, setShowLanding] = useState(true);\n\n  const handleLandingComplete = () => {\n    setShowLanding(false);\n  };\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Landing Screen */}\n      {showLanding && <LandingScreen onComplete={handleLandingComplete} />}\n\n      {/* Main Content */}\n      <div className={showLanding ? 'opacity-0' : 'opacity-100 transition-opacity duration-1000'}>\n        {/* Hero Section */}\n        <HeroSection />\n\n        {/* About Section */}\n        <AboutSection />\n\n        {/* Projects Section Placeholder */}\n        <section id=\"projects\" className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold mb-4\">Projects</h2>\n            <p className=\"text-lg text-muted-foreground\">macOS interface will go here...</p>\n          </div>\n        </section>\n\n        {/* Experience Section Placeholder */}\n        <section id=\"experience\" className=\"min-h-screen flex items-center justify-center bg-muted/20\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold mb-4\">Experience</h2>\n            <p className=\"text-lg text-muted-foreground\">Experience content will go here...</p>\n          </div>\n        </section>\n\n        {/* Contact Section Placeholder */}\n        <section id=\"contact\" className=\"min-h-screen flex items-center justify-center\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-bold mb-4\">Contact</h2>\n            <p className=\"text-lg text-muted-foreground\">Contact form will go here...</p>\n          </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,wBAAwB;QAC5B,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BAAe,8OAAC,mJAAA,CAAA,gBAAa;gBAAC,YAAY;;;;;;0BAG3C,8OAAC;gBAAI,WAAW,cAAc,cAAc;;kCAE1C,8OAAC,iJAAA,CAAA,cAAW;;;;;kCAGZ,8OAAC,kJAAA,CAAA,eAAY;;;;;kCAGb,8OAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;kCAKjD,8OAAC;wBAAQ,IAAG;wBAAa,WAAU;kCACjC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;kCAKjD,8OAAC;wBAAQ,IAAG;wBAAU,WAAU;kCAC9B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzD", "debugId": null}}]}