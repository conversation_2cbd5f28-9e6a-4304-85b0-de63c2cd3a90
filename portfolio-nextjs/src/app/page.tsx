'use client';

import React, { useState } from 'react';
import { LandingScreen } from '@/components/sections/landing-screen';
import { HeroSection } from '@/components/sections/hero-section';
import { AboutSection } from '@/components/sections/about-section';

export default function Home() {
  const [showLanding, setShowLanding] = useState(true);

  const handleLandingComplete = () => {
    setShowLanding(false);
  };

  return (
    <div className="min-h-screen">
      {/* Landing Screen */}
      {showLanding && <LandingScreen onComplete={handleLandingComplete} />}

      {/* Main Content */}
      <div className={showLanding ? 'opacity-0' : 'opacity-100 transition-opacity duration-1000'}>
        {/* Hero Section */}
        <HeroSection />

        {/* About Section */}
        <AboutSection />

        {/* Projects Section Placeholder */}
        <section id="projects" className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">Projects</h2>
            <p className="text-lg text-muted-foreground">macOS interface will go here...</p>
          </div>
        </section>

        {/* Experience Section Placeholder */}
        <section id="experience" className="min-h-screen flex items-center justify-center bg-muted/20">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">Experience</h2>
            <p className="text-lg text-muted-foreground">Experience content will go here...</p>
          </div>
        </section>

        {/* Contact Section Placeholder */}
        <section id="contact" className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">Contact</h2>
            <p className="text-lg text-muted-foreground">Contact form will go here...</p>
          </div>
        </section>
      </div>
    </div>
  );
}
