export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Landing Screen Placeholder */}
      <section id="landing" className="min-h-screen flex items-center justify-center bg-black text-white">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">Hello</h1>
          <p className="text-lg">Portfolio Migration in Progress...</p>
        </div>
      </section>

      {/* Home Section Placeholder */}
      <section id="home" className="min-h-screen flex items-center justify-center">
        <div className="text-center max-w-4xl mx-auto px-4">
          <div className="mb-4">
            <span className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-sm">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              Available for work
            </span>
          </div>
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            Hi, I'm <PERSON><PERSON>dra <span className="wave-emoji">👋</span>
          </h1>
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">No-Code Developer | AI Prompt Engineer | Mobile App Creator</h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Recent Computer Science and Engineering graduate specializing in AI-assisted development and no-code solutions.
              I leverage modern AI tools to build efficient SaaS applications, mobile apps, and automation workflows.
            </p>
          </div>
        </div>
      </section>

      {/* About Section Placeholder */}
      <section id="about" className="min-h-screen flex items-center justify-center bg-muted/20">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">About Me</h2>
          <p className="text-lg text-muted-foreground">About section content will go here...</p>
        </div>
      </section>

      {/* Projects Section Placeholder */}
      <section id="projects" className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">Projects</h2>
          <p className="text-lg text-muted-foreground">macOS interface will go here...</p>
        </div>
      </section>

      {/* Experience Section Placeholder */}
      <section id="experience" className="min-h-screen flex items-center justify-center bg-muted/20">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">Experience</h2>
          <p className="text-lg text-muted-foreground">Experience content will go here...</p>
        </div>
      </section>

      {/* Contact Section Placeholder */}
      <section id="contact" className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">Contact</h2>
          <p className="text-lg text-muted-foreground">Contact form will go here...</p>
        </div>
      </section>
    </div>
  );
}
