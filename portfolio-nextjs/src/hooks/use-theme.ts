'use client';

import { useContext } from 'react';
import { ThemeContext } from '@/contexts/theme-context';

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Re-export the context for direct access if needed
export { ThemeContext } from '@/contexts/theme-context';
