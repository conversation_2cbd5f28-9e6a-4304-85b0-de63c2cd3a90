'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Folder, Monitor, Play } from 'lucide-react';
import { cn } from '@/lib/utils';
import { MacOSDesktop } from '@/components/interactive/macos-desktop';

interface ProjectsSectionProps {
  className?: string;
}

export function ProjectsSection({ className }: ProjectsSectionProps) {
  const [showMacOS, setShowMacOS] = useState(false);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section
      id="projects"
      className={cn(
        'min-h-screen py-20 bg-muted/20 relative overflow-hidden',
        className
      )}
    >
      {/* Background decoration */}
      <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-primary/5 to-transparent" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              Projects
            </h2>
            <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6" />
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Explore my projects through an interactive macOS-style interface. Click on folders to discover different categories of work.
            </p>
          </motion.div>

          {/* macOS Interface Container */}
          <motion.div
            variants={itemVariants}
            className="macos-interface-container"
          >
            {!showMacOS ? (
              /* Preview Mode */
              <div className="relative w-full h-[600px] lg:h-[700px] bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl overflow-hidden shadow-2xl">
                {/* macOS Wallpaper Effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-300/20 via-transparent to-purple-500/20" />
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1)_0%,transparent_50%)]" />

                {/* Desktop Content */}
                <div className="relative h-full p-8">
                  {/* Project Folders */}
                  <div className="grid grid-cols-4 lg:grid-cols-6 gap-6 h-full">
                    <motion.div
                      className="flex flex-col items-center cursor-pointer group"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="w-16 h-16 lg:w-20 lg:h-20 mb-2 relative">
                        <Folder className="w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-colors" />
                        <div className="absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg" />
                      </div>
                      <span className="text-white text-sm font-medium text-center drop-shadow-md">
                        Web Apps
                      </span>
                    </motion.div>

                    <motion.div
                      className="flex flex-col items-center cursor-pointer group"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="w-16 h-16 lg:w-20 lg:h-20 mb-2 relative">
                        <Folder className="w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-colors" />
                        <div className="absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg" />
                      </div>
                      <span className="text-white text-sm font-medium text-center drop-shadow-md">
                        Mobile Apps
                      </span>
                    </motion.div>

                    <motion.div
                      className="flex flex-col items-center cursor-pointer group"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="w-16 h-16 lg:w-20 lg:h-20 mb-2 relative">
                        <Folder className="w-full h-full text-yellow-300 drop-shadow-lg group-hover:text-yellow-200 transition-colors" />
                        <div className="absolute inset-0 bg-gradient-to-br from-yellow-200/20 to-orange-300/20 rounded-lg" />
                      </div>
                      <span className="text-white text-sm font-medium text-center drop-shadow-md">
                        Automation
                      </span>
                    </motion.div>
                  </div>

                  {/* Interactive Launch Overlay */}
                  <div className="absolute inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center">
                    <motion.div
                      className="text-center text-white"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.5 }}
                    >
                      <Monitor className="w-16 h-16 mx-auto mb-4 opacity-80" />
                      <h3 className="text-2xl font-bold mb-2">Interactive macOS Interface</h3>
                      <p className="text-lg opacity-90 mb-6">Explore my projects through a realistic macOS desktop experience</p>

                      <motion.button
                        onClick={() => setShowMacOS(true)}
                        className="px-8 py-3 bg-white/20 backdrop-blur-md rounded-lg border border-white/30 text-white font-medium hover:bg-white/30 transition-all duration-300 flex items-center gap-2 mx-auto"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Play className="w-5 h-5" />
                        Launch macOS Interface
                      </motion.button>
                    </motion.div>
                  </div>
                </div>

                {/* macOS Dock Placeholder */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-white/10 backdrop-blur-md rounded-2xl px-4 py-2 border border-white/20">
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-white/20 rounded-lg" />
                      <div className="w-8 h-8 bg-white/20 rounded-lg" />
                      <div className="w-8 h-8 bg-white/20 rounded-lg" />
                      <div className="w-1 h-6 bg-white/30 rounded-full mx-1" />
                      <div className="w-8 h-8 bg-white/20 rounded-lg" />
                      <div className="w-8 h-8 bg-white/20 rounded-lg" />
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              /* Interactive macOS Desktop */
              <div className="relative w-full h-[600px] lg:h-[700px] rounded-2xl overflow-hidden shadow-2xl">
                <MacOSDesktop />

                {/* Exit Button */}
                <motion.button
                  onClick={() => setShowMacOS(false)}
                  className="absolute top-4 right-4 z-[1000] px-4 py-2 bg-black/50 backdrop-blur-md rounded-lg border border-white/20 text-white text-sm font-medium hover:bg-black/70 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Exit macOS
                </motion.button>
              </div>
            )}
          </motion.div>

          {/* Project Stats */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-2 lg:grid-cols-4 gap-6 mt-12"
          >
            {[
              { label: 'Projects Completed', value: '10+' },
              { label: 'Technologies Used', value: '15+' },
              { label: 'Client Satisfaction', value: '100%' },
              { label: 'Years Experience', value: '2+' },
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center p-6 bg-card/50 border border-border/50 rounded-lg"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <div className="text-2xl lg:text-3xl font-bold text-primary mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
