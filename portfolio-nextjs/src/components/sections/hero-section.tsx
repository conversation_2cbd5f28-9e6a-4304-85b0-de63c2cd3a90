'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>R<PERSON>, Code, Github, Linkedin, Twitter } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ScrollIndicator } from '@/components/interactive/scroll-indicator';
import { SocialIcons } from '@/components/common/social-icons';

interface HeroSectionProps {
  className?: string;
}

export function HeroSection({ className }: HeroSectionProps) {
  const handleGetInTouch = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleViewProjects = () => {
    const projectsSection = document.getElementById('projects');
    if (projectsSection) {
      projectsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section 
      id="home" 
      className={cn(
        'min-h-screen flex items-center justify-center relative overflow-hidden',
        className
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Text Container */}
          <motion.div
            className="text-container space-y-8 lg:pr-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            {/* Online Status */}
            <motion.div
              className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm font-medium"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <motion.span
                className="w-2 h-2 bg-green-500 rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [1, 0.7, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
              />
              Available for work
            </motion.div>

            {/* Main Heading */}
            <motion.h1
              className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground leading-tight"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              Hi, I'm Narendra{' '}
              <motion.span
                className="inline-block"
                animate={{
                  rotate: [0, 14, -8, 14, -4, 10, 0],
                }}
                transition={{
                  duration: 2.5,
                  repeat: Infinity,
                  repeatDelay: 3,
                  ease: 'easeInOut',
                }}
              >
                👋
              </motion.span>
            </motion.h1>

            {/* Bio Container */}
            <motion.div
              className="bio-container space-y-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
            >
              <h2 className="text-xl sm:text-2xl font-semibold text-foreground">
                No-Code Developer | AI Prompt Engineer | Mobile App Creator
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed max-w-2xl">
                Recent Computer Science and Engineering graduate specializing in AI-assisted development and no-code solutions. 
                I leverage modern AI tools to build efficient SaaS applications, mobile apps, and automation workflows. 
                With expertise in cloud backends and API integration, I create cost-effective digital solutions with minimal traditional coding.
              </p>
            </motion.div>

            {/* Button Container */}
            <motion.div
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
            >
              <motion.button
                onClick={handleGetInTouch}
                className="group relative px-8 py-3 bg-primary text-primary-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="relative z-10 flex items-center gap-2">
                  Get in Touch
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                </span>
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-primary to-primary/80"
                  initial={{ x: '-100%' }}
                  whileHover={{ x: 0 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.button>

              <motion.button
                onClick={handleViewProjects}
                className="group relative px-8 py-3 border border-border text-foreground rounded-lg font-medium overflow-hidden transition-all duration-300 hover:shadow-lg hover:border-primary/50"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="relative z-10 flex items-center gap-2">
                  View Projects
                  <Code className="w-4 h-4 transition-transform group-hover:rotate-12" />
                </span>
                <motion.div
                  className="absolute inset-0 bg-muted/50"
                  initial={{ x: '-100%' }}
                  whileHover={{ x: 0 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.button>
            </motion.div>

            {/* Social Icons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
            >
              <SocialIcons />
            </motion.div>
          </motion.div>

          {/* Image Container */}
          <motion.div
            className="image-container flex justify-center lg:justify-end"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.4 }}
          >
            <div className="relative w-80 h-80 lg:w-96 lg:h-96">
              <motion.div
                className="morph-container relative w-full h-full rounded-3xl overflow-hidden shadow-2xl"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                {/* Background Image */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500"
                  initial={{ opacity: 1 }}
                  whileHover={{ opacity: 0.8 }}
                  transition={{ duration: 0.3 }}
                />
                
                {/* Placeholder for actual images */}
                <motion.div
                  className="absolute inset-0 flex items-center justify-center text-white text-lg font-medium"
                  initial={{ opacity: 0.8 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  Profile Image
                </motion.div>

                {/* Hover overlay */}
                <motion.div
                  className="absolute inset-0 bg-black/20"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <ScrollIndicator />
    </section>
  );
}
