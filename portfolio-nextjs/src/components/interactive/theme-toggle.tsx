'use client';

import React, { useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ThemeContext } from '@/contexts/theme-context';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  className?: string;
}

export function ThemeToggle({ className }: ThemeToggleProps) {
  const themeContext = useContext(ThemeContext);

  // Safely handle theme context
  const theme = themeContext?.theme || 'light';
  const toggleTheme = themeContext?.toggleTheme || (() => {});
  const isDark = theme === 'dark';

  return (
    <motion.div
      className={cn('relative', className)}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <input
        type="checkbox"
        id="theme-toggle"
        checked={isDark}
        onChange={toggleTheme}
        className="sr-only"
      />
      <label
        htmlFor="theme-toggle"
        className="relative block w-12 h-12 cursor-pointer"
      >
        <motion.div
          className="relative w-full h-full flex items-center justify-center"
          animate={{
            rotate: isDark ? 180 : 0,
          }}
          transition={{ duration: 0.3 }}
        >
          {/* Bulb Container */}
          <div className="relative w-8 h-8">
            {/* Main Bulb */}
            <motion.div
              className={cn(
                'absolute inset-0 rounded-full border-2 transition-all duration-300',
                isDark
                  ? 'border-yellow-400 bg-yellow-400/20'
                  : 'border-gray-400 bg-gray-100'
              )}
              animate={{
                boxShadow: isDark
                  ? '0 0 20px rgba(251, 191, 36, 0.5), 0 0 40px rgba(251, 191, 36, 0.3)'
                  : '0 0 0px rgba(0, 0, 0, 0)',
              }}
              transition={{ duration: 0.3 }}
            />

            {/* Bulb Center */}
            <motion.div
              className={cn(
                'absolute top-1/2 left-1/2 w-3 h-3 rounded-full transform -translate-x-1/2 -translate-y-1/2',
                isDark ? 'bg-yellow-300' : 'bg-gray-300'
              )}
              animate={{
                scale: isDark ? 1.2 : 1,
                boxShadow: isDark
                  ? '0 0 10px rgba(253, 224, 71, 0.8)'
                  : '0 0 0px rgba(0, 0, 0, 0)',
              }}
              transition={{ duration: 0.3 }}
            />

            {/* Filaments */}
            <motion.div
              className={cn(
                'absolute top-1/2 left-1/2 w-1 h-4 transform -translate-x-1/2 -translate-y-1/2',
                isDark ? 'bg-yellow-200' : 'bg-gray-400'
              )}
              style={{
                clipPath: 'polygon(40% 0%, 60% 0%, 70% 100%, 30% 100%)',
              }}
              animate={{
                opacity: isDark ? 1 : 0.6,
              }}
              transition={{ duration: 0.3 }}
            />

            <motion.div
              className={cn(
                'absolute top-1/2 left-1/2 w-1 h-3 transform -translate-x-1/2 -translate-y-1/2 rotate-45',
                isDark ? 'bg-yellow-200' : 'bg-gray-400'
              )}
              style={{
                clipPath: 'polygon(40% 0%, 60% 0%, 70% 100%, 30% 100%)',
              }}
              animate={{
                opacity: isDark ? 1 : 0.6,
              }}
              transition={{ duration: 0.3 }}
            />

            {/* Reflections */}
            <motion.div
              className={cn(
                'absolute top-1 left-1 w-2 h-2 rounded-full',
                isDark ? 'bg-yellow-100/60' : 'bg-white/60'
              )}
              animate={{
                opacity: isDark ? 1 : 0.8,
              }}
              transition={{ duration: 0.3 }}
            />

            {/* Sparks - Only visible when dark mode is active */}
            <AnimatePresence>
              {isDark && (
                <>
                  {[...Array(8)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-1 h-1 bg-yellow-300 rounded-full"
                      style={{
                        top: '50%',
                        left: '50%',
                        transformOrigin: '0 0',
                      }}
                      initial={{
                        scale: 0,
                        x: 0,
                        y: 0,
                        rotate: i * 45,
                      }}
                      animate={{
                        scale: [0, 1, 0],
                        x: [0, 20, 30],
                        y: [0, -5, -10],
                        opacity: [0, 1, 0],
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        delay: i * 0.2,
                        ease: 'easeOut',
                      }}
                    />
                  ))}
                </>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      </label>
    </motion.div>
  );
}
