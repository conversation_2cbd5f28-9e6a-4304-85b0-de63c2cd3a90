'use client';

import React from 'react';
import { Navbar } from './navbar';
import { MobileMenu } from './mobile-menu';
import { Footer } from './footer';
import { ThemeProvider } from '@/contexts/theme-context';
import { ParticleAnimation } from '@/components/interactive/particle-animation';
import { useTheme } from '@/hooks/use-theme';

interface LayoutProps {
  children: React.ReactNode;
}

function LayoutContent({ children }: LayoutProps) {
  const { theme } = useTheme();

  return (
    <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
      {/* Particle Animation for Dark Mode */}
      {theme === 'dark' && <ParticleAnimation />}
      
      {/* Navigation */}
      <Navbar />
      <MobileMenu />
      
      {/* Main Content */}
      <main className="relative z-10">
        {children}
      </main>
      
      {/* Footer */}
      <Footer />
    </div>
  );
}

export function Layout({ children }: LayoutProps) {
  return (
    <ThemeProvider>
      <LayoutContent>{children}</LayoutContent>
    </ThemeProvider>
  );
}
