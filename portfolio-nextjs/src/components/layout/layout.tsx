'use client';

import React, { useContext } from 'react';
import { Navbar } from './navbar';
import { MobileMenu } from './mobile-menu';
import { Footer } from './footer';
import { ThemeProvider, ThemeContext } from '@/contexts/theme-context';
import { ParticleAnimation } from '@/components/interactive/particle-animation';

interface LayoutProps {
  children: React.ReactNode;
}

function LayoutContent({ children }: LayoutProps) {
  const themeContext = useContext(ThemeContext);

  // Safely handle theme context
  const theme = themeContext?.theme || 'light';

  return (
    <div className="min-h-screen bg-background text-foreground transition-colors duration-300">
      {/* Particle Animation for Dark Mode */}
      {theme === 'dark' && <ParticleAnimation />}

      {/* Navigation */}
      <Navbar />
      <MobileMenu />

      {/* Main Content */}
      <main className="relative z-10">
        {children}
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
}

export function Layout({ children }: LayoutProps) {
  return (
    <ThemeProvider>
      <LayoutContent>{children}</LayoutContent>
    </ThemeProvider>
  );
}
