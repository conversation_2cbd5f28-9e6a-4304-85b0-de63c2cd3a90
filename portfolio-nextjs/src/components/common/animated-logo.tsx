'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface AnimatedLogoProps {
  className?: string;
}

export function AnimatedLogo({ className }: AnimatedLogoProps) {
  return (
    <motion.svg
      viewBox="0 0 100 100"
      className={cn('text-foreground', className)}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <g 
        fill="none" 
        stroke="currentColor" 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth="6"
      >
        {/* Left line */}
        <motion.path
          d="M 21 40 V 59"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1, delay: 0.2 }}
        >
          <motion.animateTransform
            attributeName="transform"
            attributeType="XML"
            type="rotate"
            values="0 21 59; 180 21 59"
            dur="2s"
            repeatCount="indefinite"
          />
        </motion.path>

        {/* Right line */}
        <motion.path
          d="M 79 40 V 59"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1, delay: 0.3 }}
        >
          <motion.animateTransform
            attributeName="transform"
            attributeType="XML"
            type="rotate"
            values="0 79 59; -180 79 59"
            dur="2s"
            repeatCount="indefinite"
          />
        </motion.path>

        {/* Top line */}
        <motion.path
          d="M 50 21 V 40"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1, delay: 0.4 }}
        >
          <motion.animate
            attributeName="d"
            values="M 50 21 V 40; M 50 59 V 40"
            dur="2s"
            repeatCount="indefinite"
          />
        </motion.path>

        {/* Bottom line */}
        <motion.path
          d="M 50 60 V 79"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
        >
          <motion.animate
            attributeName="d"
            values="M 50 60 V 79; M 50 98 V 79"
            dur="2s"
            repeatCount="indefinite"
          />
        </motion.path>

        {/* Top box */}
        <motion.path
          d="M 50 21 L 79 40 L 50 60 L 21 40 Z"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 1 }}
          transition={{ duration: 1.2, delay: 0.6 }}
        >
          <motion.animate
            attributeName="stroke"
            values="currentColor; transparent"
            dur="2s"
            repeatCount="indefinite"
          />
        </motion.path>

        {/* Middle box */}
        <motion.path
          d="M 50 40 L 79 59 L 50 79 L 21 59 Z"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1.2, delay: 0.7 }}
        />

        {/* Bottom box */}
        <motion.path
          d="M 50 59 L 79 78 L 50 98 L 21 78 Z"
          initial={{ pathLength: 0, opacity: 0 }}
          animate={{ pathLength: 1, opacity: 1 }}
          transition={{ duration: 1.2, delay: 0.8 }}
        >
          <motion.animate
            attributeName="stroke"
            values="transparent; currentColor"
            dur="2s"
            repeatCount="indefinite"
          />
        </motion.path>

        {/* Main group animation */}
        <motion.animateTransform
          attributeName="transform"
          attributeType="XML"
          type="translate"
          values="0 0; 0 -19"
          dur="2s"
          repeatCount="indefinite"
        />
      </g>
    </motion.svg>
  );
}
